<?php
/**
 * صفحة قائمة الجلسات الطبية
 * Medical Sessions List Page
 */
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="fas fa-user-md me-2 text-primary"></i>
        الجلسات الطبية
    </h2>
    <a href="sessions.php?action=add" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        جلسة جديدة
    </a>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">جلسات اليوم</h6>
                    <div class="stats-number"><?= $stats['today'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-calendar-day fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, var(--primary-sky-blue) 0%, #5f9ea0 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">مكتملة</h6>
                    <div class="stats-number"><?= $stats['completed'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #FFC107 0%, #e0a800 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">قيد التنفيذ</h6>
                    <div class="stats-number"><?= $stats['in_progress'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #17A2B8 0%, #138496 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">هذا الشهر</h6>
                    <div class="stats-number"><?= $stats['total_this_month'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-stethoscope fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">التاريخ</label>
                <input type="date" class="form-control" name="date" value="<?= $date ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select class="form-select" name="filter">
                    <option value="all" <?= $filter == 'all' ? 'selected' : '' ?>>جميع الجلسات</option>
                    <option value="in_progress" <?= $filter == 'in_progress' ? 'selected' : '' ?>>قيد التنفيذ</option>
                    <option value="completed" <?= $filter == 'completed' ? 'selected' : '' ?>>مكتملة</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search" placeholder="البحث في أسماء المرضى أو التشخيص..." value="<?= $_GET['search'] ?? '' ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الجلسات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الجلسات الطبية
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($sessions)): ?>
            <div class="text-center py-5">
                <i class="fas fa-user-md fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد جلسات طبية</h5>
                <p class="text-muted">لم يتم العثور على أي جلسات تطابق معايير البحث</p>
                <a href="sessions.php?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة جلسة جديدة
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>التاريخ والوقت</th>
                            <th>اسم المريض</th>
                            <th>الشكوى الرئيسية</th>
                            <th>التشخيص</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sessions as $session): ?>
                        <tr>
                            <td>
                                <strong><?= date('Y-m-d', strtotime($session['session_date'])) ?></strong><br>
                                <small class="text-muted"><?= date('H:i', strtotime($session['session_time'])) ?></small>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar me-3">
                                        <div class="bg-success rounded-circle d-flex align-items-center justify-content-center text-white" 
                                             style="width: 35px; height: 35px;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <strong><?= $session['patient_name'] ?></strong>
                                        <?php if ($session['patient_phone']): ?>
                                            <br><small class="text-muted"><?= $session['patient_phone'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="text-truncate d-inline-block" style="max-width: 200px;" title="<?= $session['chief_complaint'] ?? '' ?>">
                                    <?= $session['chief_complaint'] ?? '' ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($session['diagnosis']): ?>
                                    <span class="text-truncate d-inline-block" style="max-width: 150px;" title="<?= $session['diagnosis'] ?>">
                                        <?= $session['diagnosis'] ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">لم يتم التشخيص</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $statusClasses = [
                                    'in_progress' => 'bg-warning',
                                    'completed' => 'bg-success'
                                ];
                                $statusLabels = [
                                    'in_progress' => 'قيد التنفيذ',
                                    'completed' => 'مكتملة'
                                ];
                                ?>
                                <span class="badge <?= $statusClasses[$session['status'] ?? ''] ?? 'bg-secondary' ?>">
                                    <?= $statusLabels[$session['status'] ?? ''] ?? ($session['status'] ?? 'غير محدد') ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="sessions.php?action=view&id=<?= $session['id'] ?>" 
                                       class="btn btn-outline-primary" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="sessions.php?action=edit&id=<?= $session['id'] ?>" 
                                       class="btn btn-outline-success" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="sessions.php?action=print&id=<?= $session['id'] ?>" 
                                       class="btn btn-outline-info" title="طباعة" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="deleteSession(<?= $session['id'] ?>)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- روابط سريعة -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                <h5>تقارير الجلسات</h5>
                <p class="text-muted">عرض إحصائيات وتقارير مفصلة</p>
                <a href="reports.php?type=sessions" class="btn btn-outline-primary">
                    <i class="fas fa-chart-bar me-2"></i>عرض التقارير
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-3x text-success mb-3"></i>
                <h5>المرضى</h5>
                <p class="text-muted">إدارة ملفات المرضى</p>
                <a href="patients.php" class="btn btn-outline-success">
                    <i class="fas fa-user me-2"></i>إدارة المرضى
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-prescription-bottle-alt fa-3x text-info mb-3"></i>
                <h5>الوصفات الطبية</h5>
                <p class="text-muted">إدارة الوصفات والأدوية</p>
                <a href="prescriptions.php" class="btn btn-outline-info">
                    <i class="fas fa-pills me-2"></i>الوصفات
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function deleteSession(id) {
    if (confirm('هل أنت متأكد من حذف هذه الجلسة الطبية؟\n\nسيتم حذف جميع البيانات المرتبطة بها نهائياً.')) {
        window.location.href = 'sessions.php?action=delete&id=' + id;
    }
}

// تحديث تلقائي للصفحة كل 10 دقائق
setTimeout(function() {
    location.reload();
}, 600000);

// تحسين عرض النصوص الطويلة
document.querySelectorAll('.text-truncate').forEach(element => {
    element.addEventListener('mouseenter', function() {
        if (this.scrollWidth > this.clientWidth) {
            this.style.maxWidth = 'none';
            this.style.whiteSpace = 'normal';
        }
    });
    
    element.addEventListener('mouseleave', function() {
        this.style.maxWidth = '';
        this.style.whiteSpace = '';
    });
});
</script>
