<?php
/**
 * صفحة الفواتير
 */

session_start();

define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('VIEWS_PATH', APP_PATH . '/views');

require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/models/Invoice.php';
require_once APP_PATH . '/models/Patient.php';
require_once APP_PATH . '/controllers/InvoicesController.php';

require_once APP_PATH . '/core/AuthGuard.php';
requireAuth();

$controller = new InvoicesController();
$action = $_GET['action'] ?? 'index';
$id = $_GET['id'] ?? null;

switch ($action) {
    case 'index':
        $controller->index();
        break;
    case 'add':
        $controller->add();
        break;
    case 'edit':
        if ($id) {
            $controller->edit($id);
        } else {
            header('Location: invoices.php');
        }
        break;
    case 'view':
        if ($id) {
            $controller->viewInvoice($id);
        } else {
            header('Location: invoices.php');
        }
        break;
    case 'delete':
        if ($id) {
            $controller->delete($id);
        } else {
            header('Location: invoices.php');
        }
        break;
    case 'print':
        if ($id) {
            $controller->printInvoice($id);
        } else {
            header('Location: invoices.php');
        }
        break;
    case 'mark_paid':
        if ($id) {
            $controller->markPaid($id);
        } else {
            header('Location: invoices.php');
        }
        break;
    default:
        $controller->index();
        break;
} 