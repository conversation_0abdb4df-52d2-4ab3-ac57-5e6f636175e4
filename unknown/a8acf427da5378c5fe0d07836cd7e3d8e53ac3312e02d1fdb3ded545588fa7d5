<?php
/**
 * كونترولر إدارة المواعيد
 * Appointments Controller
 */

class AppointmentsController extends Controller {

    public function __construct() {
        parent::__construct();
        // تحميل الموديلات المطلوبة
        require_once APP_PATH . '/models/Appointment.php';
        require_once APP_PATH . '/models/Patient.php';
    }
    
    /**
     * عرض قائمة المواعيد
     * Display appointments list
     */
    public function index() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $appointmentModel = $this->model('Appointment');
        
        // الحصول على المواعيد مع إمكانية التصفية
        $filter = $_GET['filter'] ?? 'all';
        $date = $_GET['date'] ?? date('Y-m-d');
        
        $appointments = $appointmentModel->getAppointmentsByClinic($_SESSION['clinic_id'], $filter, $date);
        
        $data = [
            'title' => 'إدارة المواعيد',
            'appointments' => $appointments,
            'filter' => $filter,
            'date' => $date,
            'stats' => $this->getAppointmentStats()
        ];
        
        $this->view->renderWithLayout('appointments/index', 'dashboard', $data);
    }
    
    /**
     * عرض تقويم المواعيد
     * Display appointments calendar
     */
    public function calendar() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $appointmentModel = $this->model('Appointment');
        
        // الحصول على المواعيد للشهر الحالي
        $month = $_GET['month'] ?? date('Y-m');
        $appointments = $appointmentModel->getAppointmentsByMonth($_SESSION['clinic_id'], $month);
        
        $data = [
            'title' => 'تقويم المواعيد',
            'appointments' => $appointments,
            'month' => $month
        ];
        
        $this->view->renderWithLayout('appointments/calendar', 'dashboard', $data);
    }
    
    /**
     * إضافة موعد جديد
     * Add new appointment
     */
    public function add() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleAddAppointment();
        }
        
        $patientModel = $this->model('Patient');
        $patients = $patientModel->getPatientsByClinic($_SESSION['clinic_id']);
        
        $data = [
            'title' => 'إضافة موعد جديد',
            'patients' => $patients,
            'time_slots' => $this->getAvailableTimeSlots()
        ];
        
        $this->view->renderWithLayout('appointments/add', 'dashboard', $data);
    }
    
    /**
     * تعديل موعد
     * Edit appointment
     */
    public function edit($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $appointmentModel = $this->model('Appointment');
        $appointment = $appointmentModel->find($id);
        
        if (!$appointment || $appointment['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الموعد غير موجود', 'error');
            $this->redirect('appointments');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleEditAppointment($id);
        }
        
        $patientModel = $this->model('Patient');
        $patients = $patientModel->getPatientsByClinic($_SESSION['clinic_id']);
        
        $data = [
            'title' => 'تعديل الموعد',
            'appointment' => $appointment,
            'patients' => $patients,
            'time_slots' => $this->getAvailableTimeSlots($appointment['appointment_date'])
        ];
        
        $this->view->renderWithLayout('appointments/edit', 'dashboard', $data);
    }
    
    /**
     * عرض تفاصيل الموعد
     * View appointment details
     */
    public function viewAppointment($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $appointmentModel = $this->model('Appointment');
        $appointment = $appointmentModel->getAppointmentDetails($id);
        
        if (!$appointment || $appointment['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الموعد غير موجود', 'error');
            $this->redirect('appointments');
        }
        
        $data = [
            'title' => 'تفاصيل الموعد',
            'appointment' => $appointment
        ];
        
        $this->view->renderWithLayout('appointments/view', 'dashboard', $data);
    }
    
    /**
     * تأكيد الموعد
     * Confirm appointment
     */
    public function confirm($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $appointmentModel = $this->model('Appointment');
        $appointment = $appointmentModel->find($id);
        
        if (!$appointment || $appointment['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الموعد غير موجود', 'error');
            $this->redirect('appointments');
        }
        
        if ($appointmentModel->updateStatus($id, 'confirmed')) {
            $this->notify('تم تأكيد الموعد بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء تأكيد الموعد', 'error');
        }
        
        $this->redirect('appointments');
    }
    
    /**
     * إلغاء الموعد
     * Cancel appointment
     */
    public function cancel($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $appointmentModel = $this->model('Appointment');
        $appointment = $appointmentModel->find($id);
        
        if (!$appointment || $appointment['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الموعد غير موجود', 'error');
            $this->redirect('appointments');
        }
        
        if ($appointmentModel->updateStatus($id, 'cancelled')) {
            $this->notify('تم إلغاء الموعد بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء إلغاء الموعد', 'error');
        }
        
        $this->redirect('appointments');
    }
    
    /**
     * حذف الموعد
     * Delete appointment
     */
    public function delete($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $appointmentModel = $this->model('Appointment');
        $appointment = $appointmentModel->find($id);
        
        if (!$appointment || $appointment['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الموعد غير موجود', 'error');
            $this->redirect('appointments');
        }
        
        if ($appointmentModel->delete($id)) {
            $this->notify('تم حذف الموعد بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء حذف الموعد', 'error');
        }
        
        $this->redirect('appointments');
    }
    
    /**
     * معالجة إضافة موعد جديد
     * Handle add appointment
     */
    private function handleAddAppointment() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'patient_id' => 'required|numeric',
            'appointment_date' => 'required|date',
            'appointment_time' => 'required',
            'duration' => 'required|numeric',
            'notes' => 'max:500'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        $appointmentModel = $this->model('Appointment');
        $patientModel = $this->model('Patient');
        $patient = $patientModel->find($data['patient_id']);
        if (!$patient) {
            $this->notify('المريض غير موجود', 'error');
            return;
        }
        // التحقق من عدم تضارب المواعيد
        if ($appointmentModel->hasConflict($_SESSION['clinic_id'], $data['appointment_date'], $data['appointment_time'], $data['duration'])) {
            $this->notify('يوجد تضارب مع موعد آخر في نفس الوقت', 'error');
            return;
        }
        
        $appointmentData = [
            'clinic_id' => $_SESSION['clinic_id'],
            'patient_id' => $data['patient_id'],
            'patient_name' => $patient['full_name'],
            'patient_phone' => $patient['phone'],
            'appointment_date' => $data['appointment_date'],
            'appointment_time' => $data['appointment_time'],
            'duration' => $data['duration'],
            'status' => 'pending',
            'notes' => $data['notes'] ?? '',
            'created_by' => $_SESSION['user_id'],
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        if ($appointmentModel->create($appointmentData)) {
            $this->notify('تم إضافة الموعد بنجاح', 'success');
            $this->redirect('appointments');
        } else {
            $this->notify('حدث خطأ أثناء إضافة الموعد', 'error');
        }
    }
    
    /**
     * معالجة تعديل الموعد
     * Handle edit appointment
     */
    private function handleEditAppointment($id) {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'patient_id' => 'required|numeric',
            'appointment_date' => 'required|date',
            'appointment_time' => 'required',
            'duration' => 'required|numeric',
            'status' => 'required|in:pending,confirmed,completed,cancelled,no_show',
            'notes' => 'max:500'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        $appointmentModel = $this->model('Appointment');
        
        // التحقق من عدم تضارب المواعيد (باستثناء الموعد الحالي)
        if ($appointmentModel->hasConflict($_SESSION['clinic_id'], $data['appointment_date'], $data['appointment_time'], $data['duration'], $id)) {
            $this->notify('يوجد تضارب مع موعد آخر في نفس الوقت', 'error');
            return;
        }
        
        $appointmentData = [
            'patient_id' => $data['patient_id'],
            'appointment_date' => $data['appointment_date'],
            'appointment_time' => $data['appointment_time'],
            'duration' => $data['duration'],
            'status' => $data['status'],
            'notes' => $data['notes'] ?? '',
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($appointmentModel->update($id, $appointmentData)) {
            $this->notify('تم تحديث الموعد بنجاح', 'success');
            $this->redirect('appointments');
        } else {
            $this->notify('حدث خطأ أثناء تحديث الموعد', 'error');
        }
    }
    
    /**
     * الحصول على إحصائيات المواعيد
     * Get appointment statistics
     */
    private function getAppointmentStats() {
        $appointmentModel = $this->model('Appointment');
        return $appointmentModel->getStats($_SESSION['clinic_id']);
    }
    
    /**
     * الحصول على الأوقات المتاحة
     * Get available time slots
     */
    private function getAvailableTimeSlots($date = null) {
        $date = $date ?? date('Y-m-d');
        
        // أوقات العمل الافتراضية (يمكن تخصيصها لاحقاً)
        $workingHours = [
            '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
            '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
            '15:00', '15:30', '16:00', '16:30', '17:00', '17:30',
            '18:00', '18:30', '19:00', '19:30', '20:00', '20:30'
        ];
        
        return $workingHours;
    }
}
?>
