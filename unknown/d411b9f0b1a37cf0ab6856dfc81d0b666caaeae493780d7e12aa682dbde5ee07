<?php
/**
 * صفحة إدارة الجلسات الطبية
 * Medical Sessions Management
 */

// تشغيل الجلسات
session_start();

// تعريف المسارات
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('VIEWS_PATH', APP_PATH . '/views');

// تحميل الإعدادات
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/models/MedicalSession.php';
require_once APP_PATH . '/models/Patient.php';
require_once APP_PATH . '/controllers/SessionsController.php';
require_once APP_PATH . '/core/AuthGuard.php';
requireAuth();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// تشغيل كونترولر الجلسات الطبية
$controller = new SessionsController();
$action = $_GET['action'] ?? 'index';
$id = $_GET['id'] ?? null;

switch ($action) {
    case 'index':
        $controller->index();
        break;
    case 'add':
        $controller->add();
        break;
    case 'edit':
        if ($id) {
            $controller->edit($id);
        } else {
            header('Location: sessions.php');
        }
        break;
    case 'view':
        if ($id) {
            $controller->view($id);
        } else {
            header('Location: sessions.php');
        }
        break;
    case 'delete':
        if ($id) {
            $controller->delete($id);
        } else {
            header('Location: sessions.php');
        }
        break;
    case 'print':
        if ($id) {
            $controller->print($id);
        } else {
            header('Location: sessions.php');
        }
        break;
    default:
        $controller->index();
        break;
}
?>
