/**
 * ملف JavaScript الرئيسي لنظام حكيم
 * Main JavaScript file for Hakim system
 */

// إعدادات عامة
const HakimApp = {
    baseUrl: window.location.origin,
    
    // تهيئة التطبيق
    init: function() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupAnimations();
        this.setupFormValidation();
    },
    
    // إعداد مستمعي الأحداث
    setupEventListeners: function() {
        // إخفاء التنبيهات تلقائياً
        this.autoHideAlerts();
        
        // تأكيد الحذف
        this.setupDeleteConfirmation();
        
        // تحسين النماذج
        this.enhanceForms();
        
        // إعداد الجداول
        this.setupTables();
    },
    
    // تهيئة المكونات
    initializeComponents: function() {
        // تهيئة التولتيبس
        this.initTooltips();
        
        // تهيئة المودالز
        this.initModals();
        
        // تهيئة التقويم
        this.initCalendar();
    },
    
    // إعداد الحركات
    setupAnimations: function() {
        // تحريك العناصر عند التمرير
        this.animateOnScroll();
        
        // تأثيرات الأزرار
        this.buttonEffects();
    },
    
    // إخفاء التنبيهات تلقائياً
    autoHideAlerts: function() {
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-success');
            alerts.forEach(alert => {
                if (alert.classList.contains('alert-success')) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 5000);
    },
    
    // تأكيد الحذف
    setupDeleteConfirmation: function() {
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('delete-confirm') || 
                e.target.closest('.delete-confirm')) {
                e.preventDefault();
                
                const message = e.target.dataset.message || 'هل أنت متأكد من الحذف؟';
                if (confirm(message)) {
                    const form = e.target.closest('form');
                    if (form) {
                        form.submit();
                    } else {
                        window.location.href = e.target.href;
                    }
                }
            }
        });
    },
    
    // تحسين النماذج
    enhanceForms: function() {
        // إضافة حالة التحميل للأزرار
        document.addEventListener('submit', function(e) {
            const submitBtn = e.target.querySelector('button[type="submit"]');
            if (submitBtn) {
                HakimApp.showLoading(submitBtn);
            }
        });
        
        // التحقق من صحة البيانات في الوقت الفعلي
        this.realTimeValidation();
    },
    
    // التحقق من صحة البيانات في الوقت الفعلي
    realTimeValidation: function() {
        const inputs = document.querySelectorAll('input[required], select[required], textarea[required]');
        
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                HakimApp.validateField(this);
            });
            
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    HakimApp.validateField(this);
                }
            });
        });
    },
    
    // التحقق من صحة حقل واحد
    validateField: function(field) {
        const value = field.value.trim();
        const type = field.type;
        let isValid = true;
        let message = '';
        
        // التحقق من الحقول المطلوبة
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'هذا الحقل مطلوب';
        }
        
        // التحقق من البريد الإلكتروني
        if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                message = 'البريد الإلكتروني غير صحيح';
            }
        }
        
        // التحقق من رقم الهاتف
        if (type === 'tel' && value) {
            const phoneRegex = /^[0-9+\-\s]+$/;
            if (!phoneRegex.test(value)) {
                isValid = false;
                message = 'رقم الهاتف غير صحيح';
            }
        }
        
        // تطبيق النتيجة
        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
            this.removeFieldError(field);
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
            this.showFieldError(field, message);
        }
        
        return isValid;
    },
    
    // إظهار خطأ الحقل
    showFieldError: function(field, message) {
        this.removeFieldError(field);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
    },
    
    // إزالة خطأ الحقل
    removeFieldError: function(field) {
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    },
    
    // إعداد الجداول
    setupTables: function() {
        // إضافة فلترة سريعة للجداول
        const searchInputs = document.querySelectorAll('.table-search');
        searchInputs.forEach(input => {
            input.addEventListener('input', function() {
                HakimApp.filterTable(this);
            });
        });
        
        // ترقيم الصفوف
        this.numberTableRows();
    },
    
    // فلترة الجدول
    filterTable: function(searchInput) {
        const table = document.querySelector(searchInput.dataset.target);
        if (!table) return;
        
        const rows = table.querySelectorAll('tbody tr');
        const searchTerm = searchInput.value.toLowerCase();
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    },
    
    // ترقيم صفوف الجدول
    numberTableRows: function() {
        const tables = document.querySelectorAll('.table-numbered');
        tables.forEach(table => {
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach((row, index) => {
                const numberCell = row.querySelector('.row-number');
                if (numberCell) {
                    numberCell.textContent = index + 1;
                }
            });
        });
    },
    
    // تهيئة التولتيبس
    initTooltips: function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // تهيئة المودالز
    initModals: function() {
        // إعادة تعيين النماذج عند إغلاق المودال
        document.addEventListener('hidden.bs.modal', function(e) {
            const forms = e.target.querySelectorAll('form');
            forms.forEach(form => {
                form.reset();
                form.classList.remove('was-validated');
                
                // إزالة رسائل الخطأ
                const invalidFeedbacks = form.querySelectorAll('.invalid-feedback');
                invalidFeedbacks.forEach(feedback => feedback.remove());
                
                // إزالة كلاسات التحقق
                const inputs = form.querySelectorAll('.is-valid, .is-invalid');
                inputs.forEach(input => {
                    input.classList.remove('is-valid', 'is-invalid');
                });
            });
        });
    },
    
    // تحريك العناصر عند التمرير
    animateOnScroll: function() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, {
            threshold: 0.1
        });
        
        const elements = document.querySelectorAll('.animate-on-scroll');
        elements.forEach(el => observer.observe(el));
    },
    
    // تأثيرات الأزرار
    buttonEffects: function() {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                // تأثير الموجة
                const ripple = document.createElement('span');
                ripple.classList.add('ripple');
                this.appendChild(ripple);
                
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    },
    
    // إظهار حالة التحميل
    showLoading: function(button) {
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
        button.disabled = true;
        
        return function hideLoading() {
            button.innerHTML = originalText;
            button.disabled = false;
        };
    },
    
    // تنسيق الأرقام
    formatNumber: function(num) {
        return new Intl.NumberFormat('ar-EG').format(num);
    },
    
    // تنسيق العملة
    formatCurrency: function(amount, currency = 'ILS') {
        return new Intl.NumberFormat('he-IL', {
            style: 'currency',
            currency: currency
        }).format(amount);
    },
    
    // تنسيق التاريخ
    formatDate: function(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        return new Date(date).toLocaleDateString('ar-EG', {
            ...defaultOptions,
            ...options
        });
    },
    
    // إرسال طلب AJAX
    ajax: function(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const config = { ...defaults, ...options };
        
        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            });
    },
    
    // إظهار إشعار
    showNotification: function(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(notification);
            bsAlert.close();
        }, 5000);
    },
    
    // تحديث الوقت الحقيقي
    updateRealTime: function() {
        const timeElements = document.querySelectorAll('.real-time');
        timeElements.forEach(element => {
            const now = new Date();
            element.textContent = now.toLocaleTimeString('ar-EG');
        });
    }
};

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    HakimApp.init();
    
    // تحديث الوقت كل ثانية
    setInterval(HakimApp.updateRealTime, 1000);
});

// إضافة أنماط CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .btn {
        position: relative;
        overflow: hidden;
    }
`;
document.head.appendChild(style);

// تصدير الكائن للاستخدام العام
window.HakimApp = HakimApp;
