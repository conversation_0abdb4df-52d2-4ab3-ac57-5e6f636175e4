<?php
/**
 * كونترولر الوصفات الطبية
 * Prescriptions Controller
 */

class PrescriptionsController extends Controller {
    
    public function __construct() {
        parent::__construct();
        // تحميل الموديلات المطلوبة
        require_once APP_PATH . '/models/Prescription.php';
        require_once APP_PATH . '/models/Patient.php';
    }

    public function index() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $prescriptionModel = $this->model('Prescription');
        
        // الحصول على الفلاتر
        $dateFrom = $_GET['date_from'] ?? date('Y-m-01');
        $dateTo = $_GET['date_to'] ?? date('Y-m-d');
        $status = $_GET['status'] ?? 'all';
        $search = $_GET['search'] ?? '';
        
        // الحصول على الوصفات
        $prescriptions = $prescriptionModel->getPrescriptionsByClinic($_SESSION['clinic_id'], $dateFrom, $dateTo, $status, $search);
        
        // الحصول على الإحصائيات
        $stats = $this->getPrescriptionStats();
        
        $data = [
            'title' => 'إدارة الوصفات الطبية',
            'prescriptions' => $prescriptions,
            'stats' => $stats,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'status' => $status,
            'search' => $search
        ];
        
        $this->view->renderWithLayout('prescriptions/index', 'dashboard', $data);
    }
    
    /**
     * إضافة وصفة طبية جديدة
     */
    public function add() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleAddPrescription();
        }
        
        $patientModel = $this->model('Patient');
        $sessionModel = $this->model('MedicalSession');
        
        $patients = $patientModel->getPatientsByClinic($_SESSION['clinic_id']);
        $sessions = $sessionModel->getSessionsByClinic($_SESSION['clinic_id']);
        $prescriptionNumber = $this->generatePrescriptionNumber();
        
        $data = [
            'title' => 'وصفة طبية جديدة',
            'patients' => $patients,
            'sessions' => $sessions,
            'prescriptionNumber' => $prescriptionNumber
        ];
        
        $this->view->renderWithLayout('prescriptions/add', 'dashboard', $data);
    }
    
    /**
     * عرض تفاصيل الوصفة الطبية
     */
    public function viewPrescription($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $prescriptionModel = $this->model('Prescription');
        $prescription = $prescriptionModel->getPrescriptionDetails($id);
        
        if (!$prescription || $prescription['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الوصفة الطبية غير موجودة', 'error');
            $this->redirect('prescriptions');
        }
        
        $data = [
            'title' => 'تفاصيل الوصفة الطبية',
            'prescription' => $prescription
        ];
        
        $this->view->renderWithLayout('prescriptions/view', 'dashboard', $data);
    }
    
    /**
     * تعديل الوصفة الطبية
     */
    public function edit($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $prescriptionModel = $this->model('Prescription');
        $prescription = $prescriptionModel->find($id);
        
        if (!$prescription || $prescription['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الوصفة الطبية غير موجودة', 'error');
            $this->redirect('prescriptions');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleEditPrescription($id);
        }
        
        $patientModel = $this->model('Patient');
        $patients = $patientModel->getPatientsByClinic($_SESSION['clinic_id']);
        
        $data = [
            'title' => 'تعديل الوصفة الطبية',
            'prescription' => $prescription,
            'patients' => $patients
        ];
        
        $this->view->renderWithLayout('prescriptions/edit', 'dashboard', $data);
    }
    
    /**
     * حذف الوصفة الطبية
     */
    public function delete($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $prescriptionModel = $this->model('Prescription');
        $prescription = $prescriptionModel->find($id);
        
        if (!$prescription || $prescription['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الوصفة الطبية غير موجودة', 'error');
            $this->redirect('prescriptions');
        }
        
        if ($prescriptionModel->delete($id)) {
            $this->notify('تم حذف الوصفة الطبية بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء حذف الوصفة الطبية', 'error');
        }
        
        $this->redirect('prescriptions');
    }
    
    /**
     * طباعة الوصفة الطبية
     */
    public function printPrescription($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $prescriptionModel = $this->model('Prescription');
        $prescription = $prescriptionModel->getPrescriptionDetails($id);
        
        if (!$prescription || $prescription['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الوصفة الطبية غير موجودة', 'error');
            $this->redirect('prescriptions');
        }
        
        $data = [
            'title' => 'طباعة الوصفة الطبية',
            'prescription' => $prescription
        ];
        
        $this->view->render('prescriptions/print', $data);
    }
    
    /**
     * تحديد الوصفة كمكتملة
     */
    public function complete($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $prescriptionModel = $this->model('Prescription');
        $prescription = $prescriptionModel->find($id);
        
        if (!$prescription || $prescription['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الوصفة الطبية غير موجودة', 'error');
            $this->redirect('prescriptions');
        }
        
        if ($prescriptionModel->update($id, ['status' => 'completed', 'completed_date' => date('Y-m-d H:i:s')])) {
            $this->notify('تم تحديد الوصفة كمكتملة بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء تحديث الوصفة الطبية', 'error');
        }
        
        $this->redirect('prescriptions');
    }
    
    /**
     * الحصول على إحصائيات الوصفات الطبية
     */
    private function getPrescriptionStats() {
        $prescriptionModel = $this->model('Prescription');
        return $prescriptionModel->getStats($_SESSION['clinic_id']);
    }
    
    /**
     * معالجة إضافة وصفة طبية جديدة
     */
    private function handleAddPrescription() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'patient_id' => 'required|numeric',
            'prescription_date' => 'required|date',
            'diagnosis' => 'max:500',
            'medications' => 'required'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        $prescriptionModel = $this->model('Prescription');
        
        $prescriptionData = [
            'clinic_id' => $_SESSION['clinic_id'],
            'patient_id' => $data['patient_id'],
            'prescription_number' => $this->generatePrescriptionNumber(),
            'prescription_date' => $data['prescription_date'],
            'diagnosis' => $data['diagnosis'],
            'medications' => $data['medications'],
            'dosage_instructions' => $data['dosage_instructions'] ?? '',
            'duration' => $data['duration'] ?? '',
            'notes' => $data['notes'] ?? '',
            'status' => 'active',
            'created_by' => $_SESSION['user_id'],
            'created_at' => date('Y-m-d H:i:s'),
            'session_id' => !empty($data['session_id']) ? $data['session_id'] : null
        ];
        
        if ($prescriptionModel->create($prescriptionData)) {
            $this->notify('تم إضافة الوصفة الطبية بنجاح', 'success');
            $this->redirect('prescriptions');
        } else {
            $this->notify('حدث خطأ أثناء إضافة الوصفة الطبية', 'error');
        }
    }
    
    /**
     * معالجة تعديل الوصفة الطبية
     */
    private function handleEditPrescription($id) {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'patient_id' => 'required|numeric',
            'prescription_date' => 'required|date',
            'diagnosis' => 'max:500',
            'medications' => 'required'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        $prescriptionModel = $this->model('Prescription');
        
        $prescriptionData = [
            'patient_id' => $data['patient_id'],
            'prescription_date' => $data['prescription_date'],
            'diagnosis' => $data['diagnosis'],
            'medications' => $data['medications'],
            'dosage_instructions' => $data['dosage_instructions'] ?? '',
            'duration' => $data['duration'] ?? '',
            'notes' => $data['notes'] ?? '',
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($prescriptionModel->update($id, $prescriptionData)) {
            $this->notify('تم تحديث الوصفة الطبية بنجاح', 'success');
            $this->redirect('prescriptions');
        } else {
            $this->notify('حدث خطأ أثناء تحديث الوصفة الطبية', 'error');
        }
    }
    
    /**
     * توليد رقم وصفة طبية فريد
     */
    private function generatePrescriptionNumber() {
        $prescriptionModel = $this->model('Prescription');
        $year = date('Y');
        $month = date('m');
        
        $lastPrescription = $prescriptionModel->getLastPrescriptionNumber($year, $month);
        
        if ($lastPrescription) {
            $lastNumber = intval(substr($lastPrescription, -4));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return sprintf('PRESC-%s%s-%04d', $year, $month, $newNumber);
    }
} 