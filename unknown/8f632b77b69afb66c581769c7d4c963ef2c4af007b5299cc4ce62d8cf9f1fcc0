<?php
/**
 * كونترولر الفواتير
 * Invoices Controller
 */

class InvoicesController extends Controller {
    
    public function __construct() {
        parent::__construct();
        // تحميل الموديلات المطلوبة
        require_once APP_PATH . '/models/Invoice.php';
        require_once APP_PATH . '/models/Patient.php';
    }

    public function index() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $invoiceModel = $this->model('Invoice');
        
        // الحصول على الفلاتر
        $dateFrom = $_GET['date_from'] ?? date('Y-m-01');
        $dateTo = $_GET['date_to'] ?? date('Y-m-d');
        $status = $_GET['status'] ?? 'all';
        $search = $_GET['search'] ?? '';
        
        // الحصول على الفواتير
        $invoices = $invoiceModel->getInvoicesByClinic($_SESSION['clinic_id'], $dateFrom, $dateTo, $status, $search);
        
        // الحصول على الإحصائيات
        $stats = $this->getInvoiceStats();
        
        $data = [
            'title' => 'إدارة الفواتير',
            'invoices' => $invoices,
            'stats' => $stats,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'status' => $status,
            'search' => $search
        ];
        
        $this->view->renderWithLayout('invoices/index', 'dashboard', $data);
    }
    
    /**
     * إضافة فاتورة جديدة
     */
    public function add() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleAddInvoice();
        }
        
        $patientModel = $this->model('Patient');
        $patients = $patientModel->getPatientsByClinic($_SESSION['clinic_id']);
        
        $data = [
            'title' => 'فاتورة جديدة',
            'patients' => $patients
        ];
        
        $this->view->renderWithLayout('invoices/add', 'dashboard', $data);
    }
    
    /**
     * عرض تفاصيل الفاتورة
     */
    public function viewInvoice($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $invoiceModel = $this->model('Invoice');
        $invoice = $invoiceModel->getInvoiceDetails($id);
        
        if (!$invoice || $invoice['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الفاتورة غير موجودة', 'error');
            $this->redirect('invoices');
        }
        
        $data = [
            'title' => 'تفاصيل الفاتورة',
            'invoice' => $invoice
        ];
        
        $this->view->renderWithLayout('invoices/view', 'dashboard', $data);
    }
    
    /**
     * تعديل الفاتورة
     */
    public function edit($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $invoiceModel = $this->model('Invoice');
        $invoice = $invoiceModel->find($id);
        
        if (!$invoice || $invoice['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الفاتورة غير موجودة', 'error');
            $this->redirect('invoices');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleEditInvoice($id);
        }
        
        $patientModel = $this->model('Patient');
        $patients = $patientModel->getPatientsByClinic($_SESSION['clinic_id']);
        
        $data = [
            'title' => 'تعديل الفاتورة',
            'invoice' => $invoice,
            'patients' => $patients
        ];
        
        $this->view->renderWithLayout('invoices/edit', 'dashboard', $data);
    }
    
    /**
     * حذف الفاتورة
     */
    public function delete($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $invoiceModel = $this->model('Invoice');
        $invoice = $invoiceModel->find($id);
        
        if (!$invoice || $invoice['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الفاتورة غير موجودة', 'error');
            $this->redirect('invoices');
        }
        
        if ($invoiceModel->delete($id)) {
            $this->notify('تم حذف الفاتورة بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء حذف الفاتورة', 'error');
        }
        
        $this->redirect('invoices');
    }
    
    /**
     * طباعة الفاتورة
     */
    public function printInvoice($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $invoiceModel = $this->model('Invoice');
        $invoice = $invoiceModel->getInvoiceDetails($id);
        
        if (!$invoice || $invoice['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الفاتورة غير موجودة', 'error');
            $this->redirect('invoices');
        }
        
        $data = [
            'title' => 'طباعة الفاتورة',
            'invoice' => $invoice
        ];
        
        $this->view->render('invoices/print', $data);
    }
    
    /**
     * تحديد الفاتورة كمدفوعة
     */
    public function markPaid($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $invoiceModel = $this->model('Invoice');
        $invoice = $invoiceModel->find($id);
        
        if (!$invoice || $invoice['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الفاتورة غير موجودة', 'error');
            $this->redirect('invoices');
        }
        
        if ($invoiceModel->update($id, ['status' => 'paid', 'paid_date' => date('Y-m-d H:i:s')])) {
            $this->notify('تم تحديد الفاتورة كمدفوعة بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء تحديث الفاتورة', 'error');
        }
        
        $this->redirect('invoices');
    }
    
    /**
     * الحصول على إحصائيات الفواتير
     */
    private function getInvoiceStats() {
        $invoiceModel = $this->model('Invoice');
        return $invoiceModel->getStats($_SESSION['clinic_id']);
    }
    
    /**
     * معالجة إضافة فاتورة جديدة
     */
    private function handleAddInvoice() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'patient_id' => 'required|numeric',
            'invoice_date' => 'required|date',
            'services' => 'required',
            'total_amount' => 'required|numeric'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        $invoiceModel = $this->model('Invoice');
        
        $invoiceData = [
            'clinic_id' => $_SESSION['clinic_id'],
            'patient_id' => $data['patient_id'],
            'invoice_number' => $this->generateInvoiceNumber(),
            'invoice_date' => $data['invoice_date'],
            'services' => $data['services'],
            'subtotal' => $data['subtotal'] ?? $data['total_amount'],
            'discount' => isset($data['discount']) && $data['discount'] !== '' ? floatval($data['discount']) : 0,
            'total_amount' => $data['total_amount'],
            'status' => 'pending',
            'notes' => $data['notes'] ?? '',
            'created_by' => $_SESSION['user_id'],
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        if ($invoiceModel->create($invoiceData)) {
            $this->notify('تم إضافة الفاتورة بنجاح', 'success');
            $this->redirect('invoices');
        } else {
            $this->notify('حدث خطأ أثناء إضافة الفاتورة', 'error');
        }
    }
    
    /**
     * معالجة تعديل الفاتورة
     */
    private function handleEditInvoice($id) {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'patient_id' => 'required|numeric',
            'invoice_date' => 'required|date',
            'services' => 'required',
            'total_amount' => 'required|numeric'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        $invoiceModel = $this->model('Invoice');
        
        $invoiceData = [
            'patient_id' => $data['patient_id'],
            'invoice_date' => $data['invoice_date'],
            'services' => $data['services'],
            'subtotal' => $data['subtotal'] ?? $data['total_amount'],
            'discount' => isset($data['discount']) && $data['discount'] !== '' ? floatval($data['discount']) : 0,
            'total_amount' => $data['total_amount'],
            'notes' => $data['notes'] ?? '',
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($invoiceModel->update($id, $invoiceData)) {
            $this->notify('تم تحديث الفاتورة بنجاح', 'success');
            $this->redirect('invoices');
        } else {
            $this->notify('حدث خطأ أثناء تحديث الفاتورة', 'error');
        }
    }
    
    /**
     * توليد رقم فاتورة فريد
     */
    private function generateInvoiceNumber() {
        $invoiceModel = $this->model('Invoice');
        $year = date('Y');
        $month = date('m');
        
        $lastInvoice = $invoiceModel->getLastInvoiceNumber($year, $month);
        
        if ($lastInvoice) {
            $lastNumber = intval(substr($lastInvoice, -4));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return sprintf('INV-%s%s-%04d', $year, $month, $newNumber);
    }
} 