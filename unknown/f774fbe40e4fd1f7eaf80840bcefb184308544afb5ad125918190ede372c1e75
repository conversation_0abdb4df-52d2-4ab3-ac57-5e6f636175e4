<?php
/**
 * كونترولر إدارة الجلسات الطبية
 * Medical Sessions Controller
 */

class SessionsController extends Controller {

    public function __construct() {
        parent::__construct();
        // تحميل الموديلات المطلوبة
        require_once APP_PATH . '/models/MedicalSession.php';
        require_once APP_PATH . '/models/Patient.php';
        require_once APP_PATH . '/models/Appointment.php';
    }
    
    /**
     * عرض قائمة الجلسات الطبية
     * Display medical sessions list
     */
    public function index() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $sessionModel = $this->model('MedicalSession');
        
        // الحصول على الجلسات مع إمكانية التصفية
        $filter = $_GET['filter'] ?? 'all';
        $date = $_GET['date'] ?? date('Y-m-d');
        
        $sessions = $sessionModel->getSessionsByClinic($_SESSION['clinic_id'], $filter, $date);
        
        $data = [
            'title' => 'الجلسات الطبية',
            'sessions' => $sessions,
            'filter' => $filter,
            'date' => $date,
            'stats' => $this->getSessionStats()
        ];
        
        $this->view->renderWithLayout('sessions/index', 'dashboard', $data);
    }
    
    /**
     * إضافة جلسة طبية جديدة
     * Add new medical session
     */
    public function add() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleAddSession();
        }
        
        $patientModel = $this->model('Patient');
        $appointmentModel = $this->model('Appointment');
        
        $patients = $patientModel->getPatientsByClinic($_SESSION['clinic_id']);
        $appointments = $appointmentModel->getAppointmentsByClinic($_SESSION['clinic_id']);
        
        $data = [
            'title' => 'جلسة طبية جديدة',
            'patients' => $patients,
            'appointments' => $appointments
        ];
        
        $this->view->renderWithLayout('sessions/add', 'dashboard', $data);
    }
    
    /**
     * تعديل جلسة طبية
     * Edit medical session
     */
    public function edit($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $sessionModel = $this->model('MedicalSession');
        $session = $sessionModel->find($id);
        
        if (!$session || $session['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الجلسة غير موجودة', 'error');
            $this->redirect('sessions');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleEditSession($id);
        }
        
        $patientModel = $this->model('Patient');
        $patients = $patientModel->getPatientsByClinic($_SESSION['clinic_id']);
        
        $data = [
            'title' => 'تعديل الجلسة الطبية',
            'session' => $session,
            'patients' => $patients
        ];
        
        $this->view->renderWithLayout('sessions/edit', 'dashboard', $data);
    }
    
    /**
     * عرض تفاصيل الجلسة الطبية
     * View medical session details
     */
    public function viewSession($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $sessionModel = $this->model('MedicalSession');
        $session = $sessionModel->getSessionDetails($id);
        
        if (!$session || $session['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الجلسة غير موجودة', 'error');
            $this->redirect('sessions');
        }
        
        $data = [
            'title' => 'تفاصيل الجلسة الطبية',
            'session' => $session
        ];
        
        $this->view->renderWithLayout('sessions/view', 'dashboard', $data);
    }
    
    /**
     * حذف الجلسة الطبية
     * Delete medical session
     */
    public function delete($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $sessionModel = $this->model('MedicalSession');
        $session = $sessionModel->find($id);
        
        if (!$session || $session['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الجلسة غير موجودة', 'error');
            $this->redirect('sessions');
        }
        
        if ($sessionModel->delete($id)) {
            $this->notify('تم حذف الجلسة بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء حذف الجلسة', 'error');
        }
        
        $this->redirect('sessions');
    }
    
    /**
     * طباعة تقرير الجلسة
     * Print session report
     */
    public function print($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $sessionModel = $this->model('MedicalSession');
        $session = $sessionModel->getSessionDetails($id);
        
        if (!$session || $session['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('الجلسة غير موجودة', 'error');
            $this->redirect('sessions');
        }
        
        $data = [
            'title' => 'طباعة تقرير الجلسة',
            'session' => $session
        ];
        
        $this->view->render('sessions/print', $data);
    }
    
    /**
     * معالجة إضافة جلسة جديدة
     * Handle add session
     */
    private function handleAddSession() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'patient_id' => 'required|numeric',
            'session_date' => 'required|date',
            'session_time' => 'required',
            'diagnosis' => 'max:1000',
            'treatment_plan' => 'max:1000',
            'amount' => 'required|numeric',
            'notes' => 'max:1000'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        $sessionModel = $this->model('MedicalSession');
        
        $sessionData = [
            'appointment_id' => !empty($data['appointment_id']) ? $data['appointment_id'] : null,
            'patient_id' => $data['patient_id'],
            'clinic_id' => $_SESSION['clinic_id'],
            'doctor_id' => $_SESSION['user_id'],
            'session_date' => $data['session_date'],
            'session_time' => $data['session_time'],
            'diagnosis' => $data['diagnosis'] ?? '',
            'treatment_plan' => $data['treatment_plan'] ?? '',
            'notes' => $data['notes'] ?? '',
            'amount' => $data['amount'],
            'payment_status' => $data['payment_status'] ?? 'unpaid',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        if ($sessionModel->create($sessionData)) {
            $this->notify('تم إضافة الجلسة الطبية بنجاح', 'success');
            $this->redirect('sessions');
        } else {
            $this->notify('حدث خطأ أثناء إضافة الجلسة', 'error');
        }
    }
    
    /**
     * معالجة تعديل الجلسة
     * Handle edit session
     */
    private function handleEditSession($id) {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'patient_id' => 'required|numeric',
            'session_date' => 'required|date',
            'session_time' => 'required',
            'chief_complaint' => 'required|max:500',
            'examination_findings' => 'max:1000',
            'diagnosis' => 'max:500',
            'treatment' => 'max:1000',
            'medications' => 'max:1000',
            'follow_up_date' => 'date',
            'notes' => 'max:1000',
            'status' => 'required|in:in_progress,completed'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        $sessionModel = $this->model('MedicalSession');
        
        $sessionData = [
            'patient_id' => $data['patient_id'],
            'session_date' => $data['session_date'],
            'session_time' => $data['session_time'],
            'chief_complaint' => $data['chief_complaint'],
            'examination_findings' => $data['examination_findings'] ?? '',
            'diagnosis' => $data['diagnosis'] ?? '',
            'treatment' => $data['treatment'] ?? '',
            'medications' => $data['medications'] ?? '',
            'follow_up_date' => !empty($data['follow_up_date']) ? $data['follow_up_date'] : null,
            'notes' => $data['notes'] ?? '',
            'status' => $data['status'],
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($sessionModel->update($id, $sessionData)) {
            $this->notify('تم تحديث الجلسة الطبية بنجاح', 'success');
            $this->redirect('sessions');
        } else {
            $this->notify('حدث خطأ أثناء تحديث الجلسة', 'error');
        }
    }
    
    /**
     * الحصول على إحصائيات الجلسات
     * Get session statistics
     */
    private function getSessionStats() {
        $sessionModel = $this->model('MedicalSession');
        return $sessionModel->getStats($_SESSION['clinic_id']);
    }
}
?>
