<div class="container-fluid p-0" style="min-height: 100vh;">
    <div class="row g-0 h-100">
        <!-- Left Side - Registration Form -->
        <div class="col-lg-7 d-flex align-items-center justify-content-center" style="padding-top: 80px; padding-bottom: 60px;">
            <div class="w-100" style="max-width: 600px;">
                <div class="text-center mb-4 animate__animated animate__fadeInDown">
                    <div class="mb-3">
                        <i class="fas fa-stethoscope text-primary" style="font-size: 3.5rem;"></i>
                    </div>
                    <h2 class="fw-bold text-primary">ابدأ تجربتك المجانية</h2>
                    <p class="text-muted">أنشئ حسابك واحصل على 15 يوم تجربة مجانية</p>
                    <div class="d-inline-flex align-items-center bg-success text-white px-3 py-1 rounded-pill">
                        <i class="fas fa-gift me-2"></i>
                        <small class="fw-bold">مجاناً لمدة 15 يوم</small>
                    </div>
                </div>
                
                <div class="card shadow-lg border-0 animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                    <div class="card-body p-5">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger animate__animated animate__shakeX">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?= $this->escape($error) ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="register.php" id="registerForm">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="clinic_name" class="form-label fw-bold">اسم العيادة</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-clinic-medical text-muted"></i>
                                        </span>
                                        <input type="text" class="form-control" id="clinic_name" name="clinic_name" 
                                               placeholder="عيادة د. أحمد محمد" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="doctor_name" class="form-label fw-bold">اسم الطبيب</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-user-md text-muted"></i>
                                        </span>
                                        <input type="text" class="form-control" id="doctor_name" name="doctor_name" 
                                               placeholder="د. أحمد محمد" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label fw-bold">البريد الإلكتروني</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-envelope text-muted"></i>
                                        </span>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               placeholder="<EMAIL>" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label fw-bold">رقم الهاتف</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-phone text-muted"></i>
                                        </span>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               placeholder="01234567890" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label fw-bold">كلمة المرور</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-lock text-muted"></i>
                                        </span>
                                        <input type="password" class="form-control" id="password" name="password" 
                                               placeholder="كلمة مرور قوية" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                            <i class="fas fa-eye" id="toggleIcon1"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">يجب أن تحتوي على 8 أحرف على الأقل</small>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="password_confirm" class="form-label fw-bold">تأكيد كلمة المرور</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-lock text-muted"></i>
                                        </span>
                                        <input type="password" class="form-control" id="password_confirm" name="password_confirm" 
                                               placeholder="أعد كتابة كلمة المرور" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password_confirm')">
                                            <i class="fas fa-eye" id="toggleIcon2"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="specialization" class="form-label fw-bold">التخصص</label>
                                <select class="form-select" id="specialization" name="specialization" required>
                                    <option value="">اختر التخصص</option>
                                    <option value="طب عام">طب عام</option>
                                    <option value="طب باطني">طب باطني</option>
                                    <option value="طب أطفال">طب أطفال</option>
                                    <option value="طب نساء وولادة">طب نساء وولادة</option>
                                    <option value="طب عيون">طب عيون</option>
                                    <option value="طب أسنان">طب أسنان</option>
                                    <option value="طب جلدية">طب جلدية</option>
                                    <option value="طب نفسي">طب نفسي</option>
                                    <option value="جراحة عامة">جراحة عامة</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="plan" class="form-label fw-bold">اختر الباقة</label>
                                <div class="row">
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="plan" id="basic" value="basic" checked>
                                            <label class="form-check-label" for="basic">
                                                <strong>أساسية</strong><br>
                                                <small class="text-muted">149 ₪/شهر</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="plan" id="pro" value="pro">
                                            <label class="form-check-label" for="pro">
                                                <strong>متقدمة</strong><br>
                                                <small class="text-muted">299 ₪/شهر</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="plan" id="enterprise" value="enterprise">
                                            <label class="form-check-label" for="enterprise">
                                                <strong>مؤسسات</strong><br>
                                                <small class="text-muted">599 ₪/شهر</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    أوافق على <a href="#" target="_blank">شروط الاستخدام</a>
                                    و <a href="#" target="_blank">سياسة الخصوصية</a>
                                </label>
                            </div>
                            
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-success btn-lg enhanced-register-btn" id="registerBtn">
                                    <i class="fas fa-rocket me-2"></i>
                                    ابدأ تجربتك المجانية (15 يوم)
                                    <span class="btn-shine"></span>
                                </button>
                                <small class="text-center text-muted mt-2 d-block">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    آمن ومشفر • بدون التزام • إلغاء في أي وقت
                                </small>
                            </div>
                        </form>
                        
                        <div class="text-center">
                            <p class="mb-0 text-muted">لديك حساب بالفعل؟
                                <a href="login.php" class="text-primary text-decoration-none fw-bold">
                                    تسجيل الدخول
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Features -->
        <div class="col-lg-5 d-none d-lg-flex align-items-stretch justify-content-center position-relative enhanced-sidebar"
             style="background: linear-gradient(135deg, #007BFF, #0056b3); margin-top: -85px; padding-top: 85px; min-height: calc(100vh + 85px); margin-bottom: -85px; padding-bottom: 85px;">
            <div class="text-center text-white animate__animated animate__fadeInRight d-flex flex-column justify-content-center content" style="padding: 3rem 2rem;">
                <div class="mb-5">
                    <i class="fas fa-user-md" style="font-size: 5rem; opacity: 0.9;"></i>
                </div>
                <h2 class="mb-4 fw-bold">أهلاً بعودتك دكتور</h2>
                <p class="mb-5 fs-5" style="opacity: 0.9;">انضم إلى آلاف الأطباء الذين يثقون بنا</p>

                <div class="row text-center mb-5">
                    <div class="col-12 mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-3 feature-item">
                            <i class="fas fa-shield-alt me-3" style="font-size: 1.5rem;"></i>
                            <span class="fs-5">دخول آمن ومشفر</span>
                        </div>
                    </div>
                    <div class="col-12 mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-3 feature-item">
                            <i class="fas fa-bolt me-3" style="font-size: 1.5rem;"></i>
                            <span class="fs-5">وصول سريع للبيانات</span>
                        </div>
                    </div>
                    <div class="col-12 mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-3 feature-item">
                            <i class="fas fa-user-shield me-3" style="font-size: 1.5rem;"></i>
                            <span class="fs-5">حماية معلومات المرضى</span>
                        </div>
                    </div>
                    <div class="col-12 mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-3 feature-item">
                            <i class="fas fa-clock me-3" style="font-size: 1.5rem;"></i>
                            <span class="fs-5">متاح في أي وقت</span>
                        </div>
                    </div>
                </div>

                <div class="stats-card" style="background: rgba(255,255,255,0.15); border-radius: 20px; padding: 2.5rem; backdrop-filter: blur(10px);">
                    <h4 class="mb-4">
                        <i class="fas fa-chart-line me-3"></i>
                        إحصائيات مذهلة
                    </h4>
                    <div class="row text-center mb-4">
                        <div class="col-6">
                            <h2 class="mb-2 fw-bold">500+</h2>
                            <p class="mb-0 fs-6">عيادة تثق بنا</p>
                        </div>
                        <div class="col-6">
                            <h2 class="mb-2 fw-bold">10K+</h2>
                            <p class="mb-0 fs-6">مريض سعيد</p>
                        </div>
                    </div>
                    <p class="mb-0 fs-6" style="opacity: 0.9;">
                        انضم إلى آلاف الأطباء الذين يديرون عياداتهم بكفاءة وأمان
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const toggleIcon = document.getElementById(fieldId === 'password' ? 'toggleIcon1' : 'toggleIcon2');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Password validation
document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('password');
    const passwordConfirm = document.getElementById('password_confirm');
    const form = document.getElementById('registerForm');
    
    function validatePasswords() {
        if (password.value !== passwordConfirm.value) {
            passwordConfirm.setCustomValidity('كلمات المرور غير متطابقة');
        } else {
            passwordConfirm.setCustomValidity('');
        }
    }
    
    password.addEventListener('input', validatePasswords);
    passwordConfirm.addEventListener('input', validatePasswords);
    
    // Form submission
    form.addEventListener('submit', function(e) {
        if (password.value.length < 8) {
            e.preventDefault();
            alert('كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل');
            return false;
        }

        if (password.value !== passwordConfirm.value) {
            e.preventDefault();
            alert('كلمات المرور غير متطابقة');
            return false;
        }

        // Show loading state
        const registerBtn = document.getElementById('registerBtn');
        const originalText = registerBtn.innerHTML;
        registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري إنشاء الحساب...';
        registerBtn.disabled = true;

        // Re-enable after 15 seconds as fallback
        setTimeout(() => {
            registerBtn.innerHTML = originalText;
            registerBtn.disabled = false;
        }, 15000);
    });
});
</script>

<style>
/* تحسينات إضافية للتصميم */
.enhanced-sidebar {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.enhanced-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.enhanced-sidebar .content {
    position: relative;
    z-index: 2;
}

.feature-item {
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 10px;
}

.feature-item:hover {
    background: rgba(255,255,255,0.1);
    transform: translateX(-5px);
}

.stats-card {
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* تأكد من أن اللون الأزرق يلتصق بالفوتر */
body {
    overflow-x: hidden;
}

main {
    position: relative;
    z-index: 0;
}

.footer {
    position: relative;
    z-index: 0;
}

@media (max-width: 991.98px) {
    .enhanced-sidebar {
        display: none !important;
    }
}
</style>
