<?php
/**
 * صفحة قائمة الفواتير
 * Invoices List Page
 */
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="fas fa-file-invoice-dollar me-2 text-success"></i>
        إدارة الفواتير
    </h2>
    <a href="invoices.php?action=add" class="btn btn-success">
        <i class="fas fa-plus me-2"></i>
        فاتورة جديدة
    </a>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">فواتير اليوم</h6>
                    <div class="stats-number"><?= $stats['today'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-calendar-day fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">مدفوعة</h6>
                    <div class="stats-number"><?= $stats['paid'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #FFC107 0%, #e0a800 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">معلقة</h6>
                    <div class="stats-number"><?= $stats['pending'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #17A2B8 0%, #138496 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">إجمالي الشهر</h6>
                    <div class="stats-number">
                      <?= number_format($stats['total_amount'] ?? 0, 2) ?> <span style="font-size:1rem;">₪</span>
                    </div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-money-bill-wave fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-2">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" name="date_from" value="<?= $_GET['date_from'] ?? '' ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" name="date_to" value="<?= $_GET['date_to'] ?? '' ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select class="form-select" name="status">
                    <option value="all" <?= ($status ?? 'all') == 'all' ? 'selected' : '' ?>>جميع الفواتير</option>
                    <option value="pending" <?= ($status ?? '') == 'pending' ? 'selected' : '' ?>>معلقة</option>
                    <option value="paid" <?= ($status ?? '') == 'paid' ? 'selected' : '' ?>>مدفوعة</option>
                    <option value="overdue" <?= ($status ?? '') == 'overdue' ? 'selected' : '' ?>>متأخرة</option>
                    <option value="cancelled" <?= ($status ?? '') == 'cancelled' ? 'selected' : '' ?>>ملغية</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search" placeholder="البحث في أسماء المرضى أو رقم الفاتورة..." value="<?= $_GET['search'] ?? '' ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الفواتير -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الفواتير
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($invoices)): ?>
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد فواتير</h5>
                <p class="text-muted">لم يتم العثور على أي فواتير تطابق معايير البحث</p>
                <a href="invoices.php?action=add" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>إضافة فاتورة جديدة
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>اسم المريض</th>
                            <th>الخدمات</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($invoices as $invoice): ?>
                        <tr>
                            <td>
                                <strong>#<?= $invoice['invoice_number'] ?></strong>
                            </td>
                            <td>
                                <strong><?= date('Y-m-d', strtotime($invoice['invoice_date'])) ?></strong><br>
                                <small class="text-muted"><?= date('l', strtotime($invoice['invoice_date'])) ?></small>
                            </td>
                            <td>
                                <strong><?= $invoice['patient_name'] ?></strong>
                                <?php if ($invoice['patient_phone']): ?>
                                    <br><small class="text-muted">
                                        <i class="fas fa-phone me-1"></i><?= $invoice['patient_phone'] ?>
                                    </small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php 
                                $services = json_decode($invoice['services'], true) ?? [];
                                $serviceNames = array_column($services, 'name');
                                echo implode(', ', array_slice($serviceNames, 0, 2));
                                if (count($serviceNames) > 2) {
                                    echo ' +' . (count($serviceNames) - 2) . ' خدمات أخرى';
                                }
                                ?>
                            </td>
                            <td>
                                <strong class="text-success"><?= number_format($invoice['total_amount'], 2) ?> ₪</strong>
                                <?php if ($invoice['discount'] > 0): ?>
                                    <br><small class="text-muted">خصم: <?= number_format($invoice['discount'], 2) ?> ₪</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $statusClasses = [
                                    'pending' => 'bg-warning',
                                    'paid' => 'bg-success',
                                    'overdue' => 'bg-danger',
                                    'cancelled' => 'bg-secondary'
                                ];
                                $statusLabels = [
                                    'pending' => 'معلقة',
                                    'paid' => 'مدفوعة',
                                    'overdue' => 'متأخرة',
                                    'cancelled' => 'ملغية'
                                ];
                                ?>
                                <span class="badge <?= $statusClasses[$invoice['status']] ?? 'bg-secondary' ?>">
                                    <?= $statusLabels[$invoice['status']] ?? $invoice['status'] ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="invoices.php?action=view&id=<?= $invoice['id'] ?>" 
                                       class="btn btn-outline-primary" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="invoices.php?action=edit&id=<?= $invoice['id'] ?>" 
                                       class="btn btn-outline-success" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="invoices.php?action=print&id=<?= $invoice['id'] ?>" 
                                       class="btn btn-outline-info" title="طباعة" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    <?php if ($invoice['status'] == 'pending'): ?>
                                        <a href="invoices.php?action=mark_paid&id=<?= $invoice['id'] ?>" 
                                           class="btn btn-outline-success" title="تحديد كمدفوعة">
                                            <i class="fas fa-check"></i>
                                        </a>
                                    <?php endif; ?>
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="deleteInvoice(<?= $invoice['id'] ?>)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function deleteInvoice(id) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
        window.location.href = 'invoices.php?action=delete&id=' + id;
    }
}
</script> 