<?php
/**
 * صفحة إضافة موعد جديد
 * Add New Appointment Page
 */
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="fas fa-plus-circle me-2 text-primary"></i>
        إضافة موعد جديد
    </h2>
    <a href="appointments.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للقائمة
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-plus me-2"></i>
                    بيانات الموعد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="appointmentForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="patient_id" class="form-label">المريض <span class="text-danger">*</span></label>
                            <select class="form-select" id="patient_id" name="patient_id" required>
                                <option value="">اختر المريض</option>
                                <?php foreach ($patients as $patient): ?>
                                    <option value="<?= $patient['id'] ?>" data-phone="<?= $patient['phone'] ?>" data-email="<?= $patient['email'] ?>">
                                        <?= $patient['full_name'] ?> - <?= $patient['phone'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">
                                <a href="patients.php?action=add" class="text-decoration-none">
                                    <i class="fas fa-user-plus me-1"></i>إضافة مريض جديد
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="appointment_date" class="form-label">تاريخ الموعد <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="appointment_date" name="appointment_date" 
                                   min="<?= date('Y-m-d') ?>" value="<?= date('Y-m-d') ?>" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="appointment_time" class="form-label">وقت الموعد <span class="text-danger">*</span></label>
                            <select class="form-select" id="appointment_time" name="appointment_time" required>
                                <option value="">اختر الوقت</option>
                                <?php foreach ($time_slots as $time): ?>
                                    <option value="<?= $time ?>"><?= date('h:i A', strtotime($time)) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="duration" class="form-label">مدة الموعد (بالدقائق) <span class="text-danger">*</span></label>
                            <select class="form-select" id="duration" name="duration" required>
                                <option value="15">15 دقيقة</option>
                                <option value="30" selected>30 دقيقة</option>
                                <option value="45">45 دقيقة</option>
                                <option value="60">60 دقيقة</option>
                                <option value="90">90 دقيقة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات إضافية حول الموعد..."></textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="appointments.php" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الموعد
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- معلومات المريض المختار -->
        <div class="card" id="patientInfo" style="display: none;">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات المريض
                </h6>
            </div>
            <div class="card-body">
                <div id="patientDetails">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
        
        <!-- نصائح -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من صحة رقم هاتف المريض
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        اختر وقتاً مناسباً لا يتعارض مع مواعيد أخرى
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        أضف ملاحظات مهمة حول حالة المريض
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        يمكنك تعديل الموعد لاحقاً إذا لزم الأمر
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- مواعيد اليوم -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-calendar-day me-2"></i>
                    مواعيد اليوم
                </h6>
            </div>
            <div class="card-body">
                <div id="todayAppointments">
                    <div class="text-center">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const patientSelect = document.getElementById('patient_id');
    const patientInfo = document.getElementById('patientInfo');
    const patientDetails = document.getElementById('patientDetails');
    const dateInput = document.getElementById('appointment_date');
    const timeSelect = document.getElementById('appointment_time');
    
    // عرض معلومات المريض عند الاختيار
    patientSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        
        if (this.value) {
            const phone = selectedOption.dataset.phone;
            const email = selectedOption.dataset.email;
            
            patientDetails.innerHTML = `
                <p class="mb-2"><strong>الاسم:</strong> ${selectedOption.text.split(' - ')[0]}</p>
                <p class="mb-2"><strong>الهاتف:</strong> <a href="tel:${phone}">${phone}</a></p>
                ${email ? `<p class="mb-0"><strong>البريد:</strong> ${email}</p>` : ''}
            `;
            
            patientInfo.style.display = 'block';
        } else {
            patientInfo.style.display = 'none';
        }
    });
    
    // تحديث الأوقات المتاحة عند تغيير التاريخ
    dateInput.addEventListener('change', function() {
        updateAvailableTimeSlots(this.value);
    });
    
    // تحميل مواعيد اليوم
    loadTodayAppointments();
    
    // التحقق من صحة النموذج
    document.getElementById('appointmentForm').addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
});

function updateAvailableTimeSlots(date) {
    // هنا يمكن إضافة AJAX لجلب الأوقات المتاحة للتاريخ المحدد
    // مؤقتاً سنبقي جميع الأوقات متاحة
}

function loadTodayAppointments() {
    // محاكاة تحميل مواعيد اليوم
    setTimeout(function() {
        document.getElementById('todayAppointments').innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-calendar-check fa-2x mb-2"></i>
                <p class="mb-0">لا توجد مواعيد اليوم</p>
            </div>
        `;
    }, 1000);
}

function validateForm() {
    const patientId = document.getElementById('patient_id').value;
    const appointmentDate = document.getElementById('appointment_date').value;
    const appointmentTime = document.getElementById('appointment_time').value;
    const duration = document.getElementById('duration').value;
    
    if (!patientId) {
        alert('يرجى اختيار المريض');
        return false;
    }
    
    if (!appointmentDate) {
        alert('يرجى اختيار تاريخ الموعد');
        return false;
    }
    
    if (!appointmentTime) {
        alert('يرجى اختيار وقت الموعد');
        return false;
    }
    
    if (!duration) {
        alert('يرجى اختيار مدة الموعد');
        return false;
    }
    
    // التحقق من أن التاريخ ليس في الماضي
    const selectedDate = new Date(appointmentDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (selectedDate < today) {
        alert('لا يمكن حجز موعد في تاريخ سابق');
        return false;
    }
    
    return true;
}

// تحسين تجربة المستخدم
document.getElementById('patient_id').addEventListener('focus', function() {
    this.style.borderColor = 'var(--primary-blue)';
});

document.getElementById('patient_id').addEventListener('blur', function() {
    this.style.borderColor = '';
});
</script>
