<?php
/**
 * كونترولر المصادقة
 * Authentication Controller
 */

class AuthController extends Controller {
    
    /**
     * صفحة تسجيل الدخول
     * Login page
     */
    public function login() {
        // إذا كان المستخدم مسجل دخول بالفعل، توجيهه للوحة التحكم
        if (isset($_SESSION['user_id'])) {
            $this->redirect('dashboard');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleLogin();
        }
        
        $data = [
            'title' => 'تسجيل الدخول - نظام حكيم',
            'description' => 'ادخل إلى لوحة تحكم عيادتك'
        ];
        
        $this->view->renderWithLayout('auth/login', 'public', $data);
    }
    
    /**
     * معالجة تسجيل الدخول
     * Handle login process
     */
    private function handleLogin() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'email' => 'required|email',
            'password' => 'required'
        ]);
        
        if (empty($errors)) {
            $userModel = $this->model('User');
            $user = $userModel->findByEmail($data['email']);
            
            if ($user && $user['is_active'] && $userModel->verifyPassword($data['password'], $user['password'])) {
                // التحقق من صحة اشتراك العيادة (فقط إذا لم يكن أدمن)
                if ($user['clinic_id'] && $user['role'] !== 'admin') {
                    $clinicModel = $this->model('Clinic');
                    if (!$clinicModel->isSubscriptionValid($user['clinic_id'])) {
                        $this->notify('انتهت صلاحية اشتراك العيادة. يرجى تجديد الاشتراك.', 'error');
                        return;
                    }
                }
                
                // إنشاء الجلسة
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_name'] = $user['full_name'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['clinic_id'] = $user['clinic_id'];
                
                // تحديث آخر تسجيل دخول
                $userModel->updateLastLogin($user['id']);
                
                $this->notify('مرحباً بك ' . $user['full_name'], 'success');
                
                // توجيه حسب الدور
                if ($user['role'] === 'admin') {
                    header('Location: admin/dashboard.php');
                } else {
                    header('Location: dashboard.php');
                }
                exit;
            } else {
                $this->notify('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'error');
            }
        } else {
            $this->notify('يرجى تصحيح الأخطاء في النموذج', 'error');
        }
    }
    
    /**
     * تسجيل الخروج
     * Logout
     */
    public function logout() {
        // تدمير الجلسة
        session_destroy();
        
        $this->notify('تم تسجيل الخروج بنجاح', 'success');
        $this->redirect('login');
    }
    
    /**
     * صفحة تسجيل عيادة جديدة (عام)
     * Public clinic registration page
     */
    public function register() {
        // إذا كان المستخدم مسجل دخول بالفعل، توجيهه للوحة التحكم
        if (isset($_SESSION['user_id'])) {
            $this->redirect('dashboard');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handlePublicRegister();
        }

        $data = [
            'title' => 'تسجيل عيادة جديدة - نظام حكيم',
            'description' => 'ابدأ تجربتك المجانية لمدة 15 يوم'
        ];

        $this->view->renderWithLayout('auth/register', 'public', $data);
    }

    /**
     * صفحة إضافة مستخدم جديد (للعيادات الموجودة)
     * Add new user page (for existing clinics)
     */
    public function addUser() {
        $this->requireAuth();
        $this->requireRole('admin'); // فقط المدير يمكنه إضافة مستخدمين جدد

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleAddUser();
        }

        $data = [
            'title' => 'إضافة مستخدم جديد',
            'roles' => [
                'doctor' => 'طبيب',
                'secretary' => 'سكرتيرة'
            ]
        ];

        $this->view->renderWithLayout('auth/add-user', 'dashboard', $data);
    }
    
    /**
     * معالجة تسجيل عيادة جديدة (عام)
     * Handle public clinic registration
     */
    private function handlePublicRegister() {
        $data = $this->sanitize($_POST);

        $errors = $this->validate($data, [
            'clinic_name' => 'required|min:3',
            'doctor_name' => 'required|min:3',
            'email' => 'required|email',
            'phone' => 'required|phone',
            'specialization' => 'required',
            'password' => 'required|min:6'
        ]);

        if (empty($errors)) {
            try {
                $this->db->beginTransaction();

                // التحقق من عدم وجود البريد الإلكتروني
                $userModel = $this->model('User');
                if ($userModel->emailExists($data['email'])) {
                    $this->notify('البريد الإلكتروني مستخدم بالفعل', 'error');
                    return;
                }

                // إنشاء العيادة
                $clinicModel = $this->model('Clinic');
                $clinicId = $clinicModel->createClinic([
                    'name' => $data['clinic_name'],
                    'description' => $data['description'] ?? '',
                    'phone' => $data['phone'],
                    'email' => $data['email'],
                    'specialization' => $data['specialization'],
                    'address' => $data['address'] ?? ''
                ]);

                if ($clinicId) {
                    // إنشاء المستخدم الطبيب
                    $userId = $userModel->createUser([
                        'full_name' => $data['doctor_name'],
                        'email' => $data['email'],
                        'phone' => $data['phone'],
                        'password' => $data['password'],
                        'role' => 'doctor',
                        'clinic_id' => $clinicId
                    ]);

                    if ($userId) {
                        $this->db->commit();
                        $this->notify('تم تسجيل العيادة بنجاح! يمكنك الآن تسجيل الدخول.', 'success');
                        header('Location: login.php');
                        exit;
                    } else {
                        throw new Exception('فشل في إنشاء المستخدم');
                    }
                } else {
                    throw new Exception('فشل في إنشاء العيادة');
                }

            } catch (Exception $e) {
                $this->db->rollback();
                $this->notify('حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.', 'error');
            }
        } else {
            $this->notify('يرجى تصحيح الأخطاء في النموذج.', 'error');
        }
    }

    /**
     * معالجة إضافة مستخدم جديد (للعيادات الموجودة)
     * Handle adding new user (for existing clinics)
     */
    private function handleAddUser() {
        $data = $this->sanitize($_POST);

        $errors = $this->validate($data, [
            'full_name' => 'required|min:3',
            'email' => 'required|email',
            'phone' => 'required|phone',
            'role' => 'required',
            'password' => 'required|min:6'
        ]);

        if (empty($errors)) {
            $userModel = $this->model('User');

            // التحقق من عدم وجود البريد الإلكتروني
            if ($userModel->emailExists($data['email'])) {
                $this->notify('البريد الإلكتروني مستخدم بالفعل', 'error');
                return;
            }

            // إضافة معرف العيادة
            $data['clinic_id'] = $_SESSION['clinic_id'];

            $userId = $userModel->createUser($data);

            if ($userId) {
                $this->notify('تم إنشاء المستخدم بنجاح', 'success');
                $this->redirect('users');
            } else {
                $this->notify('حدث خطأ أثناء إنشاء المستخدم', 'error');
            }
        } else {
            $this->notify('يرجى تصحيح الأخطاء في النموذج', 'error');
        }
    }
    
    /**
     * صفحة نسيان كلمة المرور
     * Forgot password page
     */
    public function forgotPassword() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleForgotPassword();
        }
        
        $data = [
            'title' => 'نسيت كلمة المرور؟',
            'description' => 'أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور'
        ];
        
        $this->view->renderWithLayout('auth/forgot-password', 'public', $data);
    }
    
    /**
     * معالجة نسيان كلمة المرور
     * Handle forgot password
     */
    private function handleForgotPassword() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'email' => 'required|email'
        ]);
        
        if (empty($errors)) {
            $userModel = $this->model('User');
            $user = $userModel->findByEmail($data['email']);
            
            if ($user) {
                // إنشاء رمز إعادة التعيين
                $resetToken = bin2hex(random_bytes(32));
                $resetExpiry = date('Y-m-d H:i:s', strtotime('+1 hour'));
                
                // حفظ الرمز في قاعدة البيانات
                $userModel->update($user['id'], [
                    'reset_token' => $resetToken,
                    'reset_token_expiry' => $resetExpiry
                ]);
                
                // إرسال البريد الإلكتروني (يمكن تنفيذه لاحقاً)
                // $this->sendResetEmail($user['email'], $resetToken);
                
                $this->notify('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني', 'success');
            } else {
                $this->notify('البريد الإلكتروني غير موجود', 'error');
            }
        } else {
            $this->notify('يرجى إدخال بريد إلكتروني صحيح', 'error');
        }
    }
    
    /**
     * صفحة إعادة تعيين كلمة المرور
     * Reset password page
     */
    public function resetPassword($token = null) {
        if (!$token) {
            $this->redirect('forgot-password');
        }
        
        $userModel = $this->model('User');
        $user = $userModel->findBy('reset_token', $token);
        
        if (!$user || strtotime($user['reset_token_expiry']) < time()) {
            $this->notify('رابط إعادة التعيين غير صحيح أو منتهي الصلاحية', 'error');
            $this->redirect('forgot-password');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleResetPassword($user['id']);
        }
        
        $data = [
            'title' => 'إعادة تعيين كلمة المرور',
            'token' => $token
        ];
        
        $this->view->renderWithLayout('auth/reset-password', 'public', $data);
    }
    
    /**
     * معالجة إعادة تعيين كلمة المرور
     * Handle password reset
     */
    private function handleResetPassword($userId) {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'password' => 'required|min:6',
            'confirm_password' => 'required'
        ]);
        
        if ($data['password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = 'كلمة المرور غير متطابقة';
        }
        
        if (empty($errors)) {
            $userModel = $this->model('User');
            
            $updated = $userModel->update($userId, [
                'password' => $userModel->hashPassword($data['password']),
                'reset_token' => null,
                'reset_token_expiry' => null
            ]);
            
            if ($updated) {
                $this->notify('تم تغيير كلمة المرور بنجاح', 'success');
                $this->redirect('login');
            } else {
                $this->notify('حدث خطأ أثناء تغيير كلمة المرور', 'error');
            }
        } else {
            $this->notify('يرجى تصحيح الأخطاء في النموذج', 'error');
        }
    }
    
    /**
     * تغيير كلمة المرور (للمستخدم المسجل)
     * Change password (for logged in user)
     */
    public function changePassword() {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleChangePassword();
        }
        
        $data = [
            'title' => 'تغيير كلمة المرور'
        ];
        
        $this->view->renderWithLayout('auth/change-password', 'dashboard', $data);
    }
    
    /**
     * معالجة تغيير كلمة المرور
     * Handle password change
     */
    private function handleChangePassword() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'current_password' => 'required',
            'new_password' => 'required|min:6',
            'confirm_password' => 'required'
        ]);
        
        if ($data['new_password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = 'كلمة المرور الجديدة غير متطابقة';
        }
        
        if (empty($errors)) {
            $userModel = $this->model('User');
            $user = $userModel->find($_SESSION['user_id']);
            
            if ($userModel->verifyPassword($data['current_password'], $user['password'])) {
                $updated = $userModel->changePassword($_SESSION['user_id'], $data['new_password']);
                
                if ($updated) {
                    $this->notify('تم تغيير كلمة المرور بنجاح', 'success');
                    $this->redirect('dashboard');
                } else {
                    $this->notify('حدث خطأ أثناء تغيير كلمة المرور', 'error');
                }
            } else {
                $this->notify('كلمة المرور الحالية غير صحيحة', 'error');
            }
        } else {
            $this->notify('يرجى تصحيح الأخطاء في النموذج', 'error');
        }
    }
}
?>
