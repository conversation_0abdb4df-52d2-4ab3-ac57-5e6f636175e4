<?php
/**
 * صفحة النسخ الاحتياطي
 * Backup Settings Page
 */
?>

<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-download me-2 text-primary"></i>
                إدارة النسخ الاحتياطي
            </h1>
            <p class="text-muted">أنشئ نسخة احتياطية أو استعد نسخة سابقة من بياناتك</p>
        </div>
        <div>
            <a href="settings.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للإعدادات
            </a>
        </div>
    </div>

    <!-- رسائل الإشعارات -->
    <?php $this->showNotifications(); ?>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <span><i class="fas fa-database me-2"></i>النسخ الاحتياطية المتوفرة</span>
            <a href="settings.php?action=backup&backup_action=create" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إنشاء نسخة احتياطية جديدة
            </a>
        </div>
        <div class="card-body">
            <?php if (!empty($backups)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered align-middle">
                        <thead>
                            <tr>
                                <th>اسم النسخة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الحجم</th>
                                <th>الخيارات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($backups as $backup): ?>
                                <tr>
                                    <td><?= htmlspecialchars($backup['name']) ?></td>
                                    <td><?= htmlspecialchars($backup['date']) ?></td>
                                    <td><?= htmlspecialchars($backup['size']) ?></td>
                                    <td>
                                        <a href="public/uploads/<?= urlencode($backup['name']) ?>" class="btn btn-sm btn-success" download>
                                            <i class="fas fa-download"></i> تحميل
                                        </a>
                                        <a href="settings.php?action=backup&backup_action=restore&file=<?= urlencode($backup['name']) ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-undo"></i> استعادة
                                        </a>
                                        <a href="settings.php?action=backup&backup_action=delete&file=<?= urlencode($backup['name']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف النسخة؟');">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info mb-0">
                    لا توجد نسخ احتياطية متوفرة حالياً.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div> 