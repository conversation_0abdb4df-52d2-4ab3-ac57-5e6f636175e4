<?php
/**
 * صفحة قائمة الوصفات الطبية
 * Prescriptions List Page
 */
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="fas fa-prescription-bottle-medical me-2 text-info"></i>
        إدارة الوصفات الطبية
    </h2>
    <a href="prescriptions.php?action=add" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        وصفة طبية جديدة
    </a>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">وصفات اليوم</h6>
                    <div class="stats-number"><?= $stats['today'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-calendar-day fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #17A2B8 0%, #138496 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">هذا الأسبوع</h6>
                    <div class="stats-number"><?= $stats['this_week'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-calendar-week fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">هذا الشهر</h6>
                    <div class="stats-number"><?= $stats['this_month'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-calendar-alt fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #FFC107 0%, #e0a800 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">إجمالي الأدوية</h6>
                    <div class="stats-number"><?= $stats['total_medications'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-pills fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-2">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" name="date_from" value="<?= $_GET['date_from'] ?? '' ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" name="date_to" value="<?= $_GET['date_to'] ?? '' ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select class="form-select" name="status">
                    <option value="all" <?= ($status ?? 'all') == 'all' ? 'selected' : '' ?>>جميع الوصفات</option>
                    <option value="active" <?= ($status ?? '') == 'active' ? 'selected' : '' ?>>نشطة</option>
                    <option value="completed" <?= ($status ?? '') == 'completed' ? 'selected' : '' ?>>مكتملة</option>
                    <option value="expired" <?= ($status ?? '') == 'expired' ? 'selected' : '' ?>>منتهية الصلاحية</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search" placeholder="البحث في أسماء المرضى أو الأدوية..." value="<?= $_GET['search'] ?? '' ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الوصفات الطبية -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الوصفات الطبية
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($prescriptions)): ?>
            <div class="text-center py-5">
                <i class="fas fa-prescription-bottle fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد وصفات طبية</h5>
                <p class="text-muted">لم يتم العثور على أي وصفات طبية تطابق معايير البحث</p>
                <a href="prescriptions.php?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة وصفة طبية جديدة
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الوصفة</th>
                            <th>التاريخ</th>
                            <th>اسم المريض</th>
                            <th>التشخيص</th>
                            <th>الأدوية</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($prescriptions as $prescription): ?>
                        <tr>
                            <td>
                                <strong>#<?= $prescription['prescription_number'] ?></strong>
                            </td>
                            <td>
                                <strong><?= date('Y-m-d', strtotime($prescription['prescription_date'])) ?></strong><br>
                                <small class="text-muted"><?= date('l', strtotime($prescription['prescription_date'])) ?></small>
                            </td>
                            <td>
                                <strong><?= $prescription['patient_name'] ?></strong>
                                <?php if ($prescription['patient_phone']): ?>
                                    <br><small class="text-muted">
                                        <i class="fas fa-phone me-1"></i><?= $prescription['patient_phone'] ?>
                                    </small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="text-primary"><?= $prescription['diagnosis'] ?></span>
                                <?php if ($prescription['notes']): ?>
                                    <br><small class="text-muted"><?= substr($prescription['notes'], 0, 50) ?>...</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php 
                                $medications = json_decode($prescription['medications'], true) ?? [];
                                $medicationNames = array_column($medications, 'name');
                                echo implode(', ', array_slice($medicationNames, 0, 2));
                                if (count($medicationNames) > 2) {
                                    echo ' +' . (count($medicationNames) - 2) . ' أدوية أخرى';
                                }
                                ?>
                            </td>
                            <td>
                                <?php
                                $statusClasses = [
                                    'active' => 'bg-success',
                                    'completed' => 'bg-info',
                                    'expired' => 'bg-warning',
                                    'cancelled' => 'bg-danger'
                                ];
                                $statusLabels = [
                                    'active' => 'نشطة',
                                    'completed' => 'مكتملة',
                                    'expired' => 'منتهية الصلاحية',
                                    'cancelled' => 'ملغية'
                                ];
                                ?>
                                <span class="badge <?= $statusClasses[$prescription['status']] ?? 'bg-secondary' ?>">
                                    <?= $statusLabels[$prescription['status']] ?? $prescription['status'] ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="prescriptions.php?action=view&id=<?= $prescription['id'] ?>" 
                                       class="btn btn-outline-primary" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="prescriptions.php?action=edit&id=<?= $prescription['id'] ?>" 
                                       class="btn btn-outline-success" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="prescriptions.php?action=print&id=<?= $prescription['id'] ?>" 
                                       class="btn btn-outline-info" title="طباعة" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    <?php if ($prescription['status'] == 'active'): ?>
                                        <a href="prescriptions.php?action=complete&id=<?= $prescription['id'] ?>" 
                                           class="btn btn-outline-success" title="تحديد كمكتملة">
                                            <i class="fas fa-check"></i>
                                        </a>
                                    <?php endif; ?>
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="deletePrescription(<?= $prescription['id'] ?>)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function deletePrescription(id) {
    if (confirm('هل أنت متأكد من حذف هذه الوصفة الطبية؟')) {
        window.location.href = 'prescriptions.php?action=delete&id=' + id;
    }
}
</script> 