<?php
/**
 * صفحة إعدادات المواعيد
 * Appointments Settings Page
 */
?>

<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-calendar-cog me-2 text-info"></i>
                إعدادات المواعيد
            </h1>
            <p class="text-muted">تخصيص نظام إدارة المواعيد حسب احتياجات عيادتك</p>
        </div>
        <div>
            <a href="settings.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للإعدادات
            </a>
        </div>
    </div>

    <!-- رسائل الإشعارات -->
    <?php $this->showNotifications(); ?>

    <form method="POST" action="settings.php?action=appointments">
        <?= $this->csrfField() ?>
        
        <div class="row">
            <!-- إعدادات عامة للمواعيد -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2 text-primary"></i>
                            الإعدادات العامة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="appointment_duration" class="form-label">
                                    مدة الموعد الافتراضية (دقائق)
                                </label>
                                <select name="appointment_duration" id="appointment_duration" class="form-select">
                                    <option value="15" <?= ($settings['appointment_duration'] ?? 30) == 15 ? 'selected' : '' ?>>15 دقيقة</option>
                                    <option value="20" <?= ($settings['appointment_duration'] ?? 30) == 20 ? 'selected' : '' ?>>20 دقيقة</option>
                                    <option value="30" <?= ($settings['appointment_duration'] ?? 30) == 30 ? 'selected' : '' ?>>30 دقيقة</option>
                                    <option value="45" <?= ($settings['appointment_duration'] ?? 30) == 45 ? 'selected' : '' ?>>45 دقيقة</option>
                                    <option value="60" <?= ($settings['appointment_duration'] ?? 30) == 60 ? 'selected' : '' ?>>60 دقيقة</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="advance_booking_days" class="form-label">
                                    الحجز المسبق (أيام)
                                </label>
                                <input type="number" name="advance_booking_days" id="advance_booking_days" 
                                       class="form-control" min="1" max="365" 
                                       value="<?= $settings['advance_booking_days'] ?? 30 ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="working_hours_start" class="form-label">
                                    بداية ساعات العمل
                                </label>
                                <input type="time" name="working_hours_start" id="working_hours_start" 
                                       class="form-control" 
                                       value="<?= $settings['working_hours_start'] ?? '08:00' ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="working_hours_end" class="form-label">
                                    نهاية ساعات العمل
                                </label>
                                <input type="time" name="working_hours_end" id="working_hours_end" 
                                       class="form-control" 
                                       value="<?= $settings['working_hours_end'] ?? '17:00' ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="break_start" class="form-label">
                                    بداية فترة الراحة
                                </label>
                                <input type="time" name="break_start" id="break_start" 
                                       class="form-control" 
                                       value="<?= $settings['break_start'] ?? '12:00' ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="break_end" class="form-label">
                                    نهاية فترة الراحة
                                </label>
                                <input type="time" name="break_end" id="break_end" 
                                       class="form-control" 
                                       value="<?= $settings['break_end'] ?? '13:00' ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات التأكيد والتذكيرات -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bell me-2 text-warning"></i>
                            التأكيد والتذكيرات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="auto_confirm" id="auto_confirm" 
                                           value="1" <?= ($settings['auto_confirm'] ?? false) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="auto_confirm">
                                        التأكيد التلقائي للمواعيد
                                    </label>
                                </div>
                                <small class="text-muted">تأكيد المواعيد تلقائياً عند الحجز</small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="send_reminders" id="send_reminders" 
                                           value="1" <?= ($settings['send_reminders'] ?? true) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="send_reminders">
                                        إرسال تذكيرات المواعيد
                                    </label>
                                </div>
                                <small class="text-muted">إرسال تذكيرات للمرضى قبل الموعد</small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="reminder_hours" class="form-label">
                                    تذكير قبل الموعد (ساعات)
                                </label>
                                <select name="reminder_hours" id="reminder_hours" class="form-select">
                                    <option value="2" <?= ($settings['reminder_hours'] ?? 24) == 2 ? 'selected' : '' ?>>2 ساعة</option>
                                    <option value="6" <?= ($settings['reminder_hours'] ?? 24) == 6 ? 'selected' : '' ?>>6 ساعات</option>
                                    <option value="12" <?= ($settings['reminder_hours'] ?? 24) == 12 ? 'selected' : '' ?>>12 ساعة</option>
                                    <option value="24" <?= ($settings['reminder_hours'] ?? 24) == 24 ? 'selected' : '' ?>>24 ساعة</option>
                                    <option value="48" <?= ($settings['reminder_hours'] ?? 24) == 48 ? 'selected' : '' ?>>48 ساعة</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="allow_cancellation" id="allow_cancellation" 
                                           value="1" <?= ($settings['allow_cancellation'] ?? true) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="allow_cancellation">
                                        السماح بإلغاء المواعيد
                                    </label>
                                </div>
                                <small class="text-muted">السماح للمرضى بإلغاء مواعيدهم</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الحجز الإلكتروني -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-globe me-2 text-success"></i>
                            الحجز الإلكتروني
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="online_booking" id="online_booking" 
                                           value="1" <?= ($settings['online_booking'] ?? false) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="online_booking">
                                        تفعيل الحجز الإلكتروني
                                    </label>
                                </div>
                                <small class="text-muted">السماح للمرضى بالحجز عبر الإنترنت</small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="require_confirmation" id="require_confirmation" 
                                           value="1" <?= ($settings['require_confirmation'] ?? true) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="require_confirmation">
                                        طلب تأكيد الحجز الإلكتروني
                                    </label>
                                </div>
                                <small class="text-muted">تأكيد المواعيد المحجوزة إلكترونياً</small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="max_daily_bookings" class="form-label">
                                    الحد الأقصى للحجوزات اليومية
                                </label>
                                <input type="number" name="max_daily_bookings" id="max_daily_bookings" 
                                       class="form-control" min="1" max="100" 
                                       value="<?= $settings['max_daily_bookings'] ?? 20 ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="booking_time_limit" class="form-label">
                                    حد الحجز قبل الموعد (ساعات)
                                </label>
                                <input type="number" name="booking_time_limit" id="booking_time_limit" 
                                       class="form-control" min="1" max="72" 
                                       value="<?= $settings['booking_time_limit'] ?? 2 ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="col-lg-4">
                <!-- أيام العمل -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-calendar-week me-2 text-primary"></i>
                            أيام العمل
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php
                        $workingDays = $settings['working_days'] ?? ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'];
                        $days = [
                            'sunday' => 'الأحد',
                            'monday' => 'الاثنين', 
                            'tuesday' => 'الثلاثاء',
                            'wednesday' => 'الأربعاء',
                            'thursday' => 'الخميس',
                            'friday' => 'الجمعة',
                            'saturday' => 'السبت'
                        ];
                        ?>
                        <?php foreach ($days as $key => $day): ?>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="working_days[]" 
                                       id="day_<?= $key ?>" value="<?= $key ?>" 
                                       <?= in_array($key, $workingDays) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="day_<?= $key ?>">
                                    <?= $day ?>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- إعدادات متقدمة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-sliders-h me-2 text-info"></i>
                            إعدادات متقدمة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="double_booking" id="double_booking" 
                                       value="1" <?= ($settings['double_booking'] ?? false) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="double_booking">
                                    السماح بالحجز المزدوج
                                </label>
                            </div>
                            <small class="text-muted">السماح بحجز أكثر من مريض في نفس الوقت</small>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="waiting_list" id="waiting_list" 
                                       value="1" <?= ($settings['waiting_list'] ?? true) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="waiting_list">
                                    قائمة الانتظار
                                </label>
                            </div>
                            <small class="text-muted">إضافة المرضى لقائمة الانتظار عند عدم توفر مواعيد</small>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="show_patient_notes" id="show_patient_notes" 
                                       value="1" <?= ($settings['show_patient_notes'] ?? true) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="show_patient_notes">
                                    عرض ملاحظات المريض
                                </label>
                            </div>
                            <small class="text-muted">عرض ملاحظات المريض في جدول المواعيد</small>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ الإعدادات
                            </button>
                            <a href="settings.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// التحقق من صحة الأوقات
document.addEventListener('DOMContentLoaded', function() {
    const startTime = document.getElementById('working_hours_start');
    const endTime = document.getElementById('working_hours_end');
    const breakStart = document.getElementById('break_start');
    const breakEnd = document.getElementById('break_end');
    
    function validateTimes() {
        if (startTime.value >= endTime.value) {
            endTime.setCustomValidity('يجب أن تكون نهاية العمل بعد بداية العمل');
        } else {
            endTime.setCustomValidity('');
        }
        
        if (breakStart.value && breakEnd.value) {
            if (breakStart.value >= breakEnd.value) {
                breakEnd.setCustomValidity('يجب أن تكون نهاية الراحة بعد بداية الراحة');
            } else if (breakStart.value < startTime.value || breakEnd.value > endTime.value) {
                breakEnd.setCustomValidity('فترة الراحة يجب أن تكون ضمن ساعات العمل');
            } else {
                breakEnd.setCustomValidity('');
            }
        }
    }
    
    startTime.addEventListener('change', validateTimes);
    endTime.addEventListener('change', validateTimes);
    breakStart.addEventListener('change', validateTimes);
    breakEnd.addEventListener('change', validateTimes);
});
</script> 