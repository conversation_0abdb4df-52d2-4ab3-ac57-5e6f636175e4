<?php
/**
 * صفحة تعديل الملف الشخصي
 * Profile Settings Page
 */
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="fas fa-user-edit me-2 text-primary"></i>
        الملف الشخصي
    </h2>
    <a href="settings.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للإعدادات
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    تعديل البيانات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <?php if (isset($_SESSION['notification'])): ?>
                    <div class="alert alert-<?= $_SESSION['notification']['type'] ?> alert-dismissible fade show" role="alert">
                        <?= $_SESSION['notification']['message'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['notification']); ?>
                <?php endif; ?>
                
                <form method="POST" id="profileForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?= $user['full_name'] ?? '' ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?= $user['email'] ?? '' ?>" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?= $user['phone'] ?? '' ?>">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">الدور</label>
                            <input type="text" class="form-control" value="<?= $user['role'] == 'admin' ? 'مدير' : 'طبيب' ?>" readonly>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">تاريخ التسجيل</label>
                        <input type="text" class="form-control" 
                               value="<?= date('Y-m-d H:i', strtotime($user['created_at'] ?? '')) ?>" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">آخر تسجيل دخول</label>
                        <input type="text" class="form-control" 
                               value="<?= $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : 'لم يسجل دخول من قبل' ?>" readonly>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="settings.php" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- معلومات الحساب -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الحساب
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar mx-auto mb-3">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" 
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-user fa-2x"></i>
                        </div>
                    </div>
                    <h5><?= $user['full_name'] ?? 'المستخدم' ?></h5>
                    <p class="text-muted"><?= $user['role'] == 'admin' ? 'مدير النظام' : 'طبيب' ?></p>
                </div>
                
                <hr>
                
                <div class="mb-2">
                    <small class="text-muted">البريد الإلكتروني:</small>
                    <div><?= $user['email'] ?? 'غير محدد' ?></div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">الهاتف:</small>
                    <div><?= $user['phone'] ?? 'غير محدد' ?></div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">حالة الحساب:</small>
                    <div>
                        <span class="badge bg-success">نشط</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="settings.php?action=password" class="btn btn-outline-warning">
                        <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                    </a>
                    <a href="settings.php?action=clinic" class="btn btn-outline-info">
                        <i class="fas fa-clinic-medical me-2"></i>إعدادات العيادة
                    </a>
                    <a href="settings.php?action=backup" class="btn btn-outline-secondary">
                        <i class="fas fa-download me-2"></i>نسخ احتياطي
                    </a>
                </div>
            </div>
        </div>
        
        <!-- نصائح الأمان -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    نصائح الأمان
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>استخدم كلمة مرور قوية</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>لا تشارك بيانات دخولك</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>سجل خروج بعد الانتهاء</small>
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>راجع نشاط حسابك بانتظام</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('profileForm');
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    const phoneInput = document.getElementById('phone');
    
    // تنسيق رقم الهاتف
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 0) {
            if (value.startsWith('966')) {
                value = '+' + value;
            } else if (value.startsWith('05')) {
                value = '+966' + value.substring(1);
            } else if (value.startsWith('5')) {
                value = '+966' + value;
            }
        }
        this.value = value;
    });
    
    // تحويل الاسم للأحرف الكبيرة
    nameInput.addEventListener('blur', function() {
        this.value = this.value.replace(/\b\w/g, l => l.toUpperCase());
    });
    
    // التحقق من صحة النموذج
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
    
    // تحسين تجربة المستخدم
    const inputs = form.querySelectorAll('input[type="text"], input[type="email"], input[type="tel"]');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.borderColor = 'var(--primary-blue)';
        });
        
        input.addEventListener('blur', function() {
            this.style.borderColor = '';
        });
    });
});

function validateForm() {
    const name = document.getElementById('name').value.trim();
    const email = document.getElementById('email').value.trim();
    
    if (!name) {
        alert('يرجى إدخال الاسم');
        document.getElementById('name').focus();
        return false;
    }
    
    if (name.length < 3) {
        alert('يجب أن يكون الاسم 3 أحرف على الأقل');
        document.getElementById('name').focus();
        return false;
    }
    
    if (!email) {
        alert('يرجى إدخال البريد الإلكتروني');
        document.getElementById('email').focus();
        return false;
    }
    
    if (!isValidEmail(email)) {
        alert('يرجى إدخال بريد إلكتروني صحيح');
        document.getElementById('email').focus();
        return false;
    }
    
    return true;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
</script>
