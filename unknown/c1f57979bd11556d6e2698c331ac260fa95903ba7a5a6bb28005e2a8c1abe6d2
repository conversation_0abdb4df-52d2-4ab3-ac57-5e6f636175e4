<?php
/**
 * كونترولر إدارة المرضى
 * Patients Controller
 */

class PatientsController extends Controller {

    public function __construct() {
        parent::__construct();
        // تحميل الموديلات المطلوبة
        require_once APP_PATH . '/models/Patient.php';
        require_once APP_PATH . '/models/Appointment.php';
    }
    
    /**
     * عرض قائمة المرضى
     * Display patients list
     */
    public function index() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $patientModel = $this->model('Patient');
        
        // البحث والتصفية
        $search = $_GET['search'] ?? '';
        $patients = $search ? 
            $patientModel->search($_SESSION['clinic_id'], $search) : 
            $patientModel->getPatientsByClinic($_SESSION['clinic_id']);
        
        $data = [
            'title' => 'إدارة المرضى',
            'patients' => $patients,
            'search' => $search,
            'stats' => $patientModel->getStats($_SESSION['clinic_id'])
        ];
        
        $this->view->renderWithLayout('patients/index', 'dashboard', $data);
    }
    
    /**
     * إضافة مريض جديد
     * Add new patient
     */
    public function add() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleAddPatient();
        }
        
        $data = [
            'title' => 'إضافة مريض جديد'
        ];
        
        $this->view->renderWithLayout('patients/add', 'dashboard', $data);
    }
    
    /**
     * تعديل مريض
     * Edit patient
     */
    public function edit($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $patientModel = $this->model('Patient');
        $patient = $patientModel->find($id);
        
        if (!$patient || $patient['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('المريض غير موجود', 'error');
            $this->redirect('patients');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleEditPatient($id);
        }
        
        $data = [
            'title' => 'تعديل بيانات المريض',
            'patient' => $patient
        ];
        
        $this->view->renderWithLayout('patients/edit', 'dashboard', $data);
    }
    
    /**
     * عرض تفاصيل المريض
     * View patient details
     */
    public function viewPatient($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $patientModel = $this->model('Patient');
        $patient = $patientModel->find($id);
        
        if (!$patient || $patient['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('المريض غير موجود', 'error');
            $this->redirect('patients');
        }
        
        // الحصول على التاريخ الطبي
        $medicalHistory = $patientModel->getMedicalHistory($id);
        $lastVisit = $patientModel->getLastVisit($id);
        $nextAppointment = $patientModel->getNextAppointment($id);
        
        $data = [
            'title' => 'ملف المريض - ' . $patient['full_name'],
            'patient' => $patient,
            'medical_history' => $medicalHistory,
            'last_visit' => $lastVisit,
            'next_appointment' => $nextAppointment,
            'age' => $patientModel->calculateAge($patient['date_of_birth'])
        ];
        
        $this->view->renderWithLayout('patients/view', 'dashboard', $data);
    }
    
    /**
     * حذف المريض
     * Delete patient
     */
    public function delete($id) {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $patientModel = $this->model('Patient');
        $patient = $patientModel->find($id);
        
        if (!$patient || $patient['clinic_id'] != $_SESSION['clinic_id']) {
            $this->notify('المريض غير موجود', 'error');
            $this->redirect('patients');
        }
        
        // التحقق من وجود مواعيد مرتبطة
        $appointmentModel = $this->model('Appointment');
        $appointments = $appointmentModel->getAppointmentsByPatient($id);
        
        if (!empty($appointments)) {
            $this->notify('لا يمكن حذف المريض لوجود مواعيد مرتبطة به', 'error');
            $this->redirect('patients');
        }
        
        if ($patientModel->delete($id)) {
            $this->notify('تم حذف المريض بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء حذف المريض', 'error');
        }
        
        $this->redirect('patients');
    }
    
    /**
     * معالجة إضافة مريض جديد
     * Handle add patient
     */
    private function handleAddPatient() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'name' => 'required|min:3|max:100',
            'phone' => 'required|phone',
            'email' => 'email',
            'date_of_birth' => 'date',
            'gender' => 'required|in:male,female',
            'national_id' => 'max:20',
            'address' => 'max:255',
            'medical_history' => 'max:1000',
            'allergies' => 'max:500',
            'emergency_contact' => 'max:100',
            'notes' => 'max:500'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        $patientModel = $this->model('Patient');
        
        // التحقق من عدم تكرار رقم الهاتف
        if ($patientModel->phoneExists($data['phone'], $_SESSION['clinic_id'])) {
            $this->notify('رقم الهاتف مسجل مسبقاً', 'error');
            return;
        }
        
        // التحقق من عدم تكرار البريد الإلكتروني
        if (!empty($data['email']) && $patientModel->emailExists($data['email'], $_SESSION['clinic_id'])) {
            $this->notify('البريد الإلكتروني مسجل مسبقاً', 'error');
            return;
        }
        
        $patientData = [
            'clinic_id' => $_SESSION['clinic_id'],
            'full_name' => $data['name'],
            'phone' => $data['phone'],
            'email' => !empty($data['email']) ? $data['email'] : null,
            'date_of_birth' => !empty($data['date_of_birth']) ? $data['date_of_birth'] : null,
            'gender' => $data['gender'],
            'national_id' => !empty($data['national_id']) ? $data['national_id'] : null,
            'address' => !empty($data['address']) ? $data['address'] : null,
            'medical_history' => !empty($data['medical_history']) ? $data['medical_history'] : null,
            'allergies' => !empty($data['allergies']) ? $data['allergies'] : null,
            'emergency_contact' => !empty($data['emergency_contact']) ? $data['emergency_contact'] : null,
            'notes' => !empty($data['notes']) ? $data['notes'] : null,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        if ($patientModel->create($patientData)) {
            $this->notify('تم إضافة المريض بنجاح', 'success');
            $this->redirect('patients');
        } else {
            $this->notify('حدث خطأ أثناء إضافة المريض', 'error');
        }
    }
    
    /**
     * معالجة تعديل المريض
     * Handle edit patient
     */
    private function handleEditPatient($id) {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'name' => 'required|min:3|max:100',
            'phone' => 'required|phone',
            'email' => 'email',
            'date_of_birth' => 'date',
            'gender' => 'required|in:male,female',
            'national_id' => 'max:20',
            'address' => 'max:255',
            'medical_history' => 'max:1000',
            'allergies' => 'max:500',
            'emergency_contact' => 'max:100',
            'notes' => 'max:500'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        $patientModel = $this->model('Patient');
        
        // التحقق من عدم تكرار رقم الهاتف (باستثناء المريض الحالي)
        if ($patientModel->phoneExists($data['phone'], $_SESSION['clinic_id'], $id)) {
            $this->notify('رقم الهاتف مسجل مسبقاً', 'error');
            return;
        }
        
        // التحقق من عدم تكرار البريد الإلكتروني (باستثناء المريض الحالي)
        if (!empty($data['email']) && $patientModel->emailExists($data['email'], $_SESSION['clinic_id'], $id)) {
            $this->notify('البريد الإلكتروني مسجل مسبقاً', 'error');
            return;
        }
        
        $patientData = [
            'full_name' => $data['name'],
            'phone' => $data['phone'],
            'email' => !empty($data['email']) ? $data['email'] : null,
            'date_of_birth' => !empty($data['date_of_birth']) ? $data['date_of_birth'] : null,
            'gender' => $data['gender'],
            'national_id' => !empty($data['national_id']) ? $data['national_id'] : null,
            'address' => !empty($data['address']) ? $data['address'] : null,
            'medical_history' => !empty($data['medical_history']) ? $data['medical_history'] : null,
            'allergies' => !empty($data['allergies']) ? $data['allergies'] : null,
            'emergency_contact' => !empty($data['emergency_contact']) ? $data['emergency_contact'] : null,
            'notes' => !empty($data['notes']) ? $data['notes'] : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($patientModel->update($id, $patientData)) {
            $this->notify('تم تحديث بيانات المريض بنجاح', 'success');
            $this->redirect('patients');
        } else {
            $this->notify('حدث خطأ أثناء تحديث بيانات المريض', 'error');
        }
    }
    
    /**
     * تصدير قائمة المرضى
     * Export patients list
     */
    public function export() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $patientModel = $this->model('Patient');
        $patients = $patientModel->getPatientsByClinic($_SESSION['clinic_id']);
        
        // إعداد ملف CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=patients_' . date('Y-m-d') . '.csv');
        
        $output = fopen('php://output', 'w');
        
        // إضافة BOM للدعم العربي
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // عناوين الأعمدة
        fputcsv($output, [
            'الاسم', 'الهاتف', 'البريد الإلكتروني', 'تاريخ الميلاد', 
            'الجنس', 'الهوية الوطنية', 'العنوان', 'تاريخ التسجيل'
        ]);
        
        // بيانات المرضى
        foreach ($patients as $patient) {
            fputcsv($output, [
                $patient['full_name'],
                $patient['phone'],
                $patient['email'],
                $patient['date_of_birth'],
                $patient['gender'] == 'male' ? 'ذكر' : 'أنثى',
                $patient['national_id'],
                $patient['address'],
                $patient['created_at']
            ]);
        }
        
        fclose($output);
        exit;
    }
}
?>
