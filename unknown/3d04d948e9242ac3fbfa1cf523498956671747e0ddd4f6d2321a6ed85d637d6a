<?php
/**
 * كلاس العرض
 * View class
 */

class View {
    private $data = [];
    
    /**
     * عرض صفحة
     * Render a view
     */
    public function render($view, $data = []) {
        $this->data = array_merge($this->data, $data);
        
        // استخراج المتغيرات
        extract($this->data);
        
        // تحديد مسار الملف
        $viewFile = APP_PATH . '/views/' . str_replace('.', '/', $view) . '.php';
        
        if (file_exists($viewFile)) {
            // بدء التخزين المؤقت
            ob_start();
            
            // تضمين ملف العرض
            include $viewFile;
            
            // الحصول على المحتوى
            $content = ob_get_clean();
            
            // عرض المحتوى
            echo $content;
        } else {
            throw new Exception("View file not found: {$viewFile}");
        }
    }
    
    /**
     * عرض صفحة مع تخطيط
     * Render view with layout
     */
    public function renderWithLayout($view, $layout = 'main', $data = []) {
        $this->data = array_merge($this->data, $data);
        
        // استخراج المتغيرات
        extract($this->data);
        
        // تحديد مسار ملف العرض
        $viewFile = APP_PATH . '/views/' . str_replace('.', '/', $view) . '.php';
        
        if (file_exists($viewFile)) {
            // بدء التخزين المؤقت للمحتوى
            ob_start();
            include $viewFile;
            $content = ob_get_clean();
            
            // تحديد مسار ملف التخطيط
            $layoutFile = APP_PATH . '/views/layouts/' . $layout . '.php';
            
            if (file_exists($layoutFile)) {
                include $layoutFile;
            } else {
                echo $content;
            }
        } else {
            throw new Exception("View file not found: {$viewFile}");
        }
    }
    
    /**
     * تضمين جزء من الصفحة
     * Include a partial view
     */
    public function partial($partial, $data = []) {
        $partialData = array_merge($this->data, $data);
        extract($partialData);
        
        $partialFile = APP_PATH . '/views/partials/' . str_replace('.', '/', $partial) . '.php';
        
        if (file_exists($partialFile)) {
            include $partialFile;
        }
    }
    
    /**
     * تنظيف وعرض النص
     * Escape and display text
     */
    public function escape($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * تنسيق التاريخ
     * Format date
     */
    public function formatDate($date, $format = 'Y-m-d H:i') {
        if (empty($date)) return '';
        return date($format, strtotime($date));
    }
    
    /**
     * تنسيق التاريخ بالعربية
     * Format date in Arabic
     */
    public function formatDateArabic($date) {
        if (empty($date)) return '';
        
        $months = [
            1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
            5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
            9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
        ];
        
        $timestamp = strtotime($date);
        $day = date('d', $timestamp);
        $month = $months[(int)date('m', $timestamp)];
        $year = date('Y', $timestamp);
        
        return "{$day} {$month} {$year}";
    }
    
    /**
     * تنسيق المبلغ
     * Format currency
     */
    public function formatCurrency($amount, $currency = '₪') {
        return number_format($amount, 2) . ' ' . $currency;
    }
    
    /**
     * إنشاء رابط
     * Create URL
     */
    public function url($path = '') {
        return APP_URL . '/' . ltrim($path, '/');
    }
    
    /**
     * إنشاء رابط للأصول
     * Create asset URL
     */
    public function asset($path) {
        $baseUrl = rtrim(APP_URL, '/');
        $cleanPath = ltrim($path, '/');
        return $baseUrl . '/public/' . $cleanPath;
    }
    
    /**
     * التحقق من الصفحة النشطة
     * Check if current page is active
     */
    public function isActive($path) {
        $currentPath = $_GET['url'] ?? '';
        return $currentPath === ltrim($path, '/');
    }
    
    /**
     * عرض الإشعارات
     * Display notifications
     */
    public function showNotifications() {
        if (isset($_SESSION['notification'])) {
            $notification = $_SESSION['notification'];
            unset($_SESSION['notification']);
            
            $type = $notification['type'];
            $message = $notification['message'];
            
            echo "<div class='alert alert-{$type} alert-dismissible fade show' role='alert'>";
            echo $this->escape($message);
            echo "<button type='button' class='btn-close' data-bs-dismiss='alert'></button>";
            echo "</div>";
        }
    }
    
    /**
     * إنشاء token CSRF
     * Generate CSRF token
     */
    public function csrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * إنشاء حقل CSRF مخفي
     * Generate CSRF hidden field
     */
    public function csrfField() {
        return "<input type='hidden' name='csrf_token' value='" . $this->csrfToken() . "'>";
    }
}
?>
