<?php
/**
 * صفحة قائمة المرضى
 * Patients List Page
 */
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="fas fa-users me-2 text-primary"></i>
        إدارة المرضى
    </h2>
    <div class="d-flex gap-2">
        <a href="patients.php?action=export" class="btn btn-outline-success">
            <i class="fas fa-download me-2"></i>تصدير
        </a>
        <a href="patients.php?action=add" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>مريض جديد
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">إجمالي المرضى</h6>
                    <div class="stats-number"><?= $stats['total'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-users fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, var(--primary-sky-blue) 0%, #5f9ea0 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">جدد هذا الشهر</h6>
                    <div class="stats-number"><?= $stats['new_this_month'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-user-plus fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #17A2B8 0%, #138496 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">ذكور</h6>
                    <div class="stats-number"><?= $stats['male'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-male fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #E91E63 0%, #C2185B 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">إناث</h6>
                    <div class="stats-number"><?= $stats['female'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-female fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- شريط البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-8">
                <label class="form-label">البحث في المرضى</label>
                <input type="text" class="form-control" name="search" 
                       placeholder="البحث بالاسم، الهاتف، البريد الإلكتروني، أو رقم الهوية..." 
                       value="<?= $search ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="patients.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>مسح
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة المرضى -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة المرضى
            <?php if ($search): ?>
                <span class="badge bg-info ms-2">نتائج البحث: <?= count($patients) ?></span>
            <?php endif; ?>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($patients)): ?>
            <div class="text-center py-5">
                <i class="fas fa-user-times fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">
                    <?= $search ? 'لا توجد نتائج للبحث' : 'لا يوجد مرضى مسجلين' ?>
                </h5>
                <p class="text-muted">
                    <?= $search ? 'جرب البحث بكلمات مختلفة' : 'ابدأ بإضافة مريض جديد' ?>
                </p>
                <?php if (!$search): ?>
                    <a href="patients.php?action=add" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>إضافة مريض جديد
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>العمر</th>
                            <th>الجنس</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($patients as $patient): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar me-3">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <strong><?= $patient['full_name'] ?></strong>
                                        <?php if ($patient['national_id']): ?>
                                            <br><small class="text-muted">هوية: <?= $patient['national_id'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <a href="tel:<?= $patient['phone'] ?>" class="text-decoration-none">
                                    <i class="fas fa-phone me-1"></i><?= $patient['phone'] ?>
                                </a>
                            </td>
                            <td>
                                <?php if ($patient['email']): ?>
                                    <a href="mailto:<?= $patient['email'] ?>" class="text-decoration-none">
                                        <i class="fas fa-envelope me-1"></i><?= $patient['email'] ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">غير محدد</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($patient['date_of_birth']): ?>
                                    <?php
                                    $birthDate = new DateTime($patient['date_of_birth']);
                                    $today = new DateTime();
                                    $age = $today->diff($birthDate)->y;
                                    ?>
                                    <?= $age ?> سنة
                                <?php else: ?>
                                    <span class="text-muted">غير محدد</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge <?= $patient['gender'] == 'male' ? 'bg-info' : 'bg-pink' ?>">
                                    <?= $patient['gender'] == 'male' ? 'ذكر' : 'أنثى' ?>
                                </span>
                            </td>
                            <td>
                                <small><?= date('Y-m-d', strtotime($patient['created_at'])) ?></small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="patients.php?action=view&id=<?= $patient['id'] ?>" 
                                       class="btn btn-outline-primary" title="عرض الملف">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="patients.php?action=edit&id=<?= $patient['id'] ?>" 
                                       class="btn btn-outline-success" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="appointments.php?action=add&patient_id=<?= $patient['id'] ?>" 
                                       class="btn btn-outline-info" title="حجز موعد">
                                        <i class="fas fa-calendar-plus"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="deletePatient(<?= $patient['id'] ?>)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- روابط سريعة -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-chart-pie fa-3x text-primary mb-3"></i>
                <h5>تقارير المرضى</h5>
                <p class="text-muted">عرض إحصائيات وتقارير مفصلة</p>
                <a href="reports.php?type=patients" class="btn btn-outline-primary">
                    <i class="fas fa-chart-line me-2"></i>عرض التقارير
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-calendar-alt fa-3x text-success mb-3"></i>
                <h5>المواعيد</h5>
                <p class="text-muted">إدارة مواعيد المرضى</p>
                <a href="appointments.php" class="btn btn-outline-success">
                    <i class="fas fa-calendar me-2"></i>إدارة المواعيد
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-user-md fa-3x text-info mb-3"></i>
                <h5>الجلسات الطبية</h5>
                <p class="text-muted">سجل الجلسات والتشخيصات</p>
                <a href="sessions.php" class="btn btn-outline-info">
                    <i class="fas fa-stethoscope me-2"></i>الجلسات الطبية
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.bg-pink {
    background-color: #E91E63 !important;
}
</style>

<script>
function deletePatient(id) {
    if (confirm('هل أنت متأكد من حذف هذا المريض؟\n\nتنبيه: سيتم حذف جميع البيانات المرتبطة بالمريض نهائياً.')) {
        window.location.href = 'patients.php?action=delete&id=' + id;
    }
}

// تحسين تجربة البحث
document.querySelector('input[name="search"]').addEventListener('keyup', function(e) {
    if (e.key === 'Enter') {
        this.form.submit();
    }
});

// إضافة تأثيرات بصرية
document.querySelectorAll('.table tbody tr').forEach(row => {
    row.addEventListener('mouseenter', function() {
        this.style.backgroundColor = 'rgba(0, 123, 255, 0.05)';
    });
    
    row.addEventListener('mouseleave', function() {
        this.style.backgroundColor = '';
    });
});
</script>
