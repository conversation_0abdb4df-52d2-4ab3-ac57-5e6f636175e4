<?php
/**
 * صفحة قائمة المواعيد
 * Appointments List Page
 */
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="fas fa-calendar-alt me-2 text-primary"></i>
        إدارة المواعيد
    </h2>
    <a href="appointments.php?action=add" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        موعد جديد
    </a>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">مواعيد اليوم</h6>
                    <div class="stats-number"><?= $stats['today'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-calendar-day fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, var(--primary-sky-blue) 0%, #5f9ea0 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">مؤكدة</h6>
                    <div class="stats-number"><?= $stats['confirmed'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #FFC107 0%, #e0a800 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">معلقة</h6>
                    <div class="stats-number"><?= $stats['pending'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #17A2B8 0%, #138496 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">هذا الشهر</h6>
                    <div class="stats-number"><?= $stats['total_this_month'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-calendar-alt fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">التاريخ</label>
                <input type="date" class="form-control" name="date" value="<?= $date ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select class="form-select" name="filter">
                    <option value="all" <?= $filter == 'all' ? 'selected' : '' ?>>جميع المواعيد</option>
                    <option value="pending" <?= $filter == 'pending' ? 'selected' : '' ?>>معلقة</option>
                    <option value="confirmed" <?= $filter == 'confirmed' ? 'selected' : '' ?>>مؤكدة</option>
                    <option value="completed" <?= $filter == 'completed' ? 'selected' : '' ?>>مكتملة</option>
                    <option value="cancelled" <?= $filter == 'cancelled' ? 'selected' : '' ?>>ملغية</option>
                    <option value="no_show" <?= $filter == 'no_show' ? 'selected' : '' ?>>لم يحضر</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search" placeholder="البحث في أسماء المرضى..." value="<?= $_GET['search'] ?? '' ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة المواعيد -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة المواعيد
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($appointments)): ?>
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مواعيد</h5>
                <p class="text-muted">لم يتم العثور على أي مواعيد تطابق معايير البحث</p>
                <a href="appointments.php?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة موعد جديد
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الوقت</th>
                            <th>اسم المريض</th>
                            <th>الهاتف</th>
                            <th>المدة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($appointments as $appointment): ?>
                        <tr>
                            <td>
                                <strong><?= date('Y-m-d', strtotime($appointment['appointment_date'])) ?></strong><br>
                                <small class="text-muted"><?= date('l', strtotime($appointment['appointment_date'])) ?></small>
                            </td>
                            <td>
                                <span class="badge bg-info"><?= date('H:i', strtotime($appointment['appointment_time'])) ?></span>
                            </td>
                            <td>
                                <strong><?= $appointment['patient_name'] ?></strong>
                                <?php if ($appointment['patient_email']): ?>
                                    <br><small class="text-muted"><?= $appointment['patient_email'] ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="tel:<?= $appointment['patient_phone'] ?>" class="text-decoration-none">
                                    <i class="fas fa-phone me-1"></i><?= $appointment['patient_phone'] ?>
                                </a>
                            </td>
                            <td><?= $appointment['duration'] ?> دقيقة</td>
                            <td>
                                <?php
                                $statusClasses = [
                                    'pending' => 'bg-warning',
                                    'confirmed' => 'bg-info',
                                    'completed' => 'bg-success',
                                    'cancelled' => 'bg-danger',
                                    'no_show' => 'bg-secondary'
                                ];
                                $statusLabels = [
                                    'pending' => 'معلق',
                                    'confirmed' => 'مؤكد',
                                    'completed' => 'مكتمل',
                                    'cancelled' => 'ملغي',
                                    'no_show' => 'لم يحضر'
                                ];
                                ?>
                                <span class="badge <?= $statusClasses[$appointment['status']] ?? 'bg-secondary' ?>">
                                    <?= $statusLabels[$appointment['status']] ?? $appointment['status'] ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="appointments.php?action=view&id=<?= $appointment['id'] ?>" 
                                       class="btn btn-outline-primary" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="appointments.php?action=edit&id=<?= $appointment['id'] ?>" 
                                       class="btn btn-outline-success" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if ($appointment['status'] == 'pending'): ?>
                                        <a href="appointments.php?action=confirm&id=<?= $appointment['id'] ?>" 
                                           class="btn btn-outline-info" title="تأكيد">
                                            <i class="fas fa-check"></i>
                                        </a>
                                    <?php endif; ?>
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="deleteAppointment(<?= $appointment['id'] ?>)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- روابط سريعة -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-calendar-week fa-3x text-primary mb-3"></i>
                <h5>عرض التقويم</h5>
                <p class="text-muted">عرض المواعيد في شكل تقويم شهري</p>
                <a href="appointments.php?action=calendar" class="btn btn-outline-primary">
                    <i class="fas fa-calendar me-2"></i>فتح التقويم
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-chart-bar fa-3x text-success mb-3"></i>
                <h5>تقارير المواعيد</h5>
                <p class="text-muted">عرض إحصائيات وتقارير مفصلة</p>
                <a href="reports.php?type=appointments" class="btn btn-outline-success">
                    <i class="fas fa-chart-line me-2"></i>عرض التقارير
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function deleteAppointment(id) {
    if (confirm('هل أنت متأكد من حذف هذا الموعد؟')) {
        window.location.href = 'appointments.php?action=delete&id=' + id;
    }
}

// تحديث تلقائي للصفحة كل 5 دقائق
setTimeout(function() {
    location.reload();
}, 300000);
</script>
