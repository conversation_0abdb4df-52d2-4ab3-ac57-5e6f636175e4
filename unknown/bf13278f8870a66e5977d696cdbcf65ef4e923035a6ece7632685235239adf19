<?php
/**
 * كونترولر التقارير
 * Reports Controller
 */

class ReportsController extends Controller {

    public function __construct() {
        parent::__construct();
        // تحميل الموديلات المطلوبة
        require_once APP_PATH . '/models/Patient.php';
        require_once APP_PATH . '/models/Appointment.php';
        require_once APP_PATH . '/models/MedicalSession.php';
    }
    
    /**
     * الصفحة الرئيسية للتقارير
     * Main reports page
     */
    public function index() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $data = [
            'title' => 'التقارير والإحصائيات',
            'summary' => $this->getReportsSummary()
        ];
        
        $this->view->renderWithLayout('reports/index', 'dashboard', $data);
    }
    
    /**
     * تقرير المرضى
     * Patients report
     */
    public function patients() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $patientModel = $this->model('Patient');
        
        $dateFrom = $_GET['date_from'] ?? date('Y-m-01');
        $dateTo = $_GET['date_to'] ?? date('Y-m-t');
        
        $data = [
            'title' => 'تقرير المرضى',
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'stats' => $patientModel->getStats($_SESSION['clinic_id']),
            'new_patients' => $this->getNewPatients($dateFrom, $dateTo),
            'frequent_patients' => $patientModel->getMostFrequentPatients($_SESSION['clinic_id'], (int) 10),
            'age_distribution' => $this->getAgeDistribution()
        ];
        
        $this->view->renderWithLayout('reports/patients', 'dashboard', $data);
    }
    
    /**
     * تقرير المواعيد
     * Appointments report
     */
    public function appointments() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $appointmentModel = $this->model('Appointment');
        
        $dateFrom = $_GET['date_from'] ?? date('Y-m-01');
        $dateTo = $_GET['date_to'] ?? date('Y-m-t');
        
        $data = [
            'title' => 'تقرير المواعيد',
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'stats' => $appointmentModel->getStats($_SESSION['clinic_id']),
            'monthly_data' => $this->getMonthlyAppointments(),
            'status_distribution' => $this->getAppointmentStatusDistribution($dateFrom, $dateTo),
            'peak_hours' => $this->getPeakHours($dateFrom, $dateTo)
        ];
        
        $this->view->renderWithLayout('reports/appointments', 'dashboard', $data);
    }
    
    /**
     * تقرير الجلسات الطبية
     * Medical sessions report
     */
    public function sessions() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $sessionModel = $this->model('MedicalSession');
        
        $dateFrom = $_GET['date_from'] ?? date('Y-m-01');
        $dateTo = $_GET['date_to'] ?? date('Y-m-t');
        
        $data = [
            'title' => 'تقرير الجلسات الطبية',
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'stats' => $sessionModel->getStats($_SESSION['clinic_id']),
            'monthly_data' => $sessionModel->getMonthlyStats($_SESSION['clinic_id']),
            'common_diagnoses' => $sessionModel->getMostCommonDiagnoses($_SESSION['clinic_id'], (int) 10),
            'prescribed_medications' => $sessionModel->getMostPrescribedMedications($_SESSION['clinic_id'], (int) 10)
        ];
        
        $this->view->renderWithLayout('reports/sessions', 'dashboard', $data);
    }
    
    /**
     * تقرير الإيرادات
     * Income report
     */
    public function income() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $dateFrom = $_GET['date_from'] ?? date('Y-m-01');
        $dateTo = $_GET['date_to'] ?? date('Y-m-t');
        
        $data = [
            'title' => 'تقرير الإيرادات',
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'income_stats' => $this->getIncomeStats($dateFrom, $dateTo),
            'monthly_income' => $this->getMonthlyIncome(),
            'payment_methods' => $this->getPaymentMethodsDistribution($dateFrom, $dateTo)
        ];
        
        $this->view->renderWithLayout('reports/income', 'dashboard', $data);
    }
    
    /**
     * تصدير التقرير
     * Export report
     */
    public function export() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $type = $_GET['type'] ?? 'patients';
        $format = $_GET['format'] ?? 'csv';
        $dateFrom = $_GET['date_from'] ?? date('Y-m-01');
        $dateTo = $_GET['date_to'] ?? date('Y-m-t');
        
        switch ($type) {
            case 'patients':
                $this->exportPatientsReport($format, $dateFrom, $dateTo);
                break;
            case 'appointments':
                $this->exportAppointmentsReport($format, $dateFrom, $dateTo);
                break;
            case 'sessions':
                $this->exportSessionsReport($format, $dateFrom, $dateTo);
                break;
            case 'income':
                $this->exportIncomeReport($format, $dateFrom, $dateTo);
                break;
            default:
                $this->notify('نوع التقرير غير صحيح', 'error');
                $this->redirect('reports');
        }
    }
    
    /**
     * الحصول على ملخص التقارير
     * Get reports summary
     */
    private function getReportsSummary() {
        $patientModel = $this->model('Patient');
        $appointmentModel = $this->model('Appointment');
        $sessionModel = $this->model('MedicalSession');
        
        return [
            'patients' => $patientModel->getStats($_SESSION['clinic_id']),
            'appointments' => $appointmentModel->getStats($_SESSION['clinic_id']),
            'sessions' => $sessionModel->getStats($_SESSION['clinic_id'])
        ];
    }
    
    /**
     * الحصول على المرضى الجدد
     * Get new patients
     */
    private function getNewPatients($dateFrom, $dateTo) {
        $sql = "SELECT COUNT(*) as count, DATE(created_at) as date
                FROM patients
                WHERE clinic_id = ? AND DATE(created_at) BETWEEN ? AND ?
                GROUP BY DATE(created_at)
                ORDER BY date";
        
        return $this->db->query($sql, [$_SESSION['clinic_id'], $dateFrom, $dateTo])->fetchAll();
    }
    
    /**
     * الحصول على توزيع الأعمار
     * Get age distribution
     */
    private function getAgeDistribution() {
        $sql = "SELECT 
                    CASE 
                        WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) < 18 THEN 'أقل من 18'
                        WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 18 AND 30 THEN '18-30'
                        WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 31 AND 50 THEN '31-50'
                        WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 51 AND 70 THEN '51-70'
                        ELSE 'أكثر من 70'
                    END as age_group,
                    COUNT(*) as count
                FROM patients
                WHERE clinic_id = ? AND date_of_birth IS NOT NULL
                GROUP BY age_group";
        
        return $this->db->query($sql, [$_SESSION['clinic_id']])->fetchAll();
    }
    
    /**
     * الحصول على المواعيد الشهرية
     * Get monthly appointments
     */
    private function getMonthlyAppointments() {
        $sql = "SELECT 
                    MONTH(appointment_date) as month,
                    YEAR(appointment_date) as year,
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled
                FROM appointments
                WHERE clinic_id = ? AND appointment_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                GROUP BY YEAR(appointment_date), MONTH(appointment_date)
                ORDER BY year, month";
        
        return $this->db->query($sql, [$_SESSION['clinic_id']])->fetchAll();
    }
    
    /**
     * الحصول على توزيع حالات المواعيد
     * Get appointment status distribution
     */
    private function getAppointmentStatusDistribution($dateFrom, $dateTo) {
        $sql = "SELECT status, COUNT(*) as count
                FROM appointments
                WHERE clinic_id = ? AND appointment_date BETWEEN ? AND ?
                GROUP BY status";
        
        return $this->db->query($sql, [$_SESSION['clinic_id'], $dateFrom, $dateTo])->fetchAll();
    }
    
    /**
     * الحصول على أوقات الذروة
     * Get peak hours
     */
    private function getPeakHours($dateFrom, $dateTo) {
        $sql = "SELECT HOUR(appointment_time) as hour, COUNT(*) as count
                FROM appointments
                WHERE clinic_id = ? AND appointment_date BETWEEN ? AND ?
                GROUP BY HOUR(appointment_time)
                ORDER BY count DESC
                LIMIT 5";
        
        return $this->db->query($sql, [$_SESSION['clinic_id'], $dateFrom, $dateTo])->fetchAll();
    }
    
    /**
     * الحصول على إحصائيات الإيرادات
     * Get income statistics
     */
    private function getIncomeStats($dateFrom, $dateTo) {
        // هذا مثال - يحتاج لجدول الفواتير والمدفوعات
        return [
            'total_income' => 0,
            'paid_amount' => 0,
            'pending_amount' => 0,
            'average_per_session' => 0
        ];
    }
    
    /**
     * الحصول على الإيرادات الشهرية
     * Get monthly income
     */
    private function getMonthlyIncome() {
        // هذا مثال - يحتاج لجدول الفواتير والمدفوعات
        return [];
    }
    
    /**
     * الحصول على توزيع طرق الدفع
     * Get payment methods distribution
     */
    private function getPaymentMethodsDistribution($dateFrom, $dateTo) {
        // هذا مثال - يحتاج لجدول الفواتير والمدفوعات
        return [];
    }
    
    /**
     * تصدير تقرير المرضى
     * Export patients report
     */
    private function exportPatientsReport($format, $dateFrom, $dateTo) {
        $patientModel = $this->model('Patient');
        $patients = $patientModel->getPatientsByClinic($_SESSION['clinic_id']);
        
        if ($format === 'csv') {
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename=patients_report_' . date('Y-m-d') . '.csv');
            
            $output = fopen('php://output', 'w');
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
            
            fputcsv($output, ['الاسم', 'الهاتف', 'البريد الإلكتروني', 'الجنس', 'تاريخ التسجيل']);
            
            foreach ($patients as $patient) {
                fputcsv($output, [
                    $patient['full_name'],
                    $patient['phone'],
                    $patient['email'],
                    $patient['gender'] == 'male' ? 'ذكر' : 'أنثى',
                    $patient['created_at']
                ]);
            }
            
            fclose($output);
            exit;
        }
    }
    
    /**
     * تصدير تقرير المواعيد
     * Export appointments report
     */
    private function exportAppointmentsReport($format, $dateFrom, $dateTo) {
        // تنفيذ تصدير تقرير المواعيد
    }
    
    /**
     * تصدير تقرير الجلسات
     * Export sessions report
     */
    private function exportSessionsReport($format, $dateFrom, $dateTo) {
        // تنفيذ تصدير تقرير الجلسات
    }
    
    /**
     * تصدير تقرير الإيرادات
     * Export income report
     */
    private function exportIncomeReport($format, $dateFrom, $dateTo) {
        // تنفيذ تصدير تقرير الإيرادات
    }
}
?>
