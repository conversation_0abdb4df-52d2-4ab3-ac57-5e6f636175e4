<?php
/**
 * إضافة جلسة طبية جديدة
 * Add New Medical Session
 */
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-plus-circle"></i>
                        إضافة جلسة طبية جديدة
                    </h4>
                    <a href="sessions.php" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i>
                        العودة للجلسات
                    </a>
                </div>
                <div class="card-body">
                    <form method="POST" action="sessions.php?action=add">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="patient_id">المريض <span class="text-danger">*</span></label>
                                    <select name="patient_id" id="patient_id" class="form-control" required>
                                        <option value="">اختر المريض</option>
                                        <?php foreach ($patients as $patient): ?>
                                            <option value="<?= $patient['id'] ?>">
                                                <?= htmlspecialchars($patient['full_name']) ?> - <?= $patient['phone'] ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="appointment_id">الموعد المرتبط</label>
                                    <select name="appointment_id" id="appointment_id" class="form-control">
                                        <option value="">اختر الموعد (اختياري)</option>
                                        <?php foreach ($appointments as $appointment): ?>
                                            <option value="<?= $appointment['id'] ?>">
                                                <?= htmlspecialchars($appointment['patient_name']) ?> - 
                                                <?= date('Y-m-d', strtotime($appointment['appointment_date'])) ?> 
                                                <?= $appointment['appointment_time'] ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="session_date">تاريخ الجلسة <span class="text-danger">*</span></label>
                                    <input type="date" name="session_date" id="session_date" class="form-control" 
                                           value="<?= date('Y-m-d') ?>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="session_time">وقت الجلسة <span class="text-danger">*</span></label>
                                    <input type="time" name="session_time" id="session_time" class="form-control" 
                                           value="<?= date('H:i') ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="diagnosis">التشخيص</label>
                            <textarea name="diagnosis" id="diagnosis" class="form-control" rows="3" 
                                      placeholder="أدخل التشخيص الطبي..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="treatment_plan">خطة العلاج</label>
                            <textarea name="treatment_plan" id="treatment_plan" class="form-control" rows="3" 
                                      placeholder="أدخل خطة العلاج..."></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="amount">المبلغ <span class="text-danger">*</span></label>
                                    <input type="number" name="amount" id="amount" class="form-control" 
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="payment_status">حالة الدفع</label>
                                    <select name="payment_status" id="payment_status" class="form-control">
                                        <option value="unpaid">غير مدفوع</option>
                                        <option value="paid">مدفوع</option>
                                        <option value="debt">دين</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات</label>
                            <textarea name="notes" id="notes" class="form-control" rows="3" 
                                      placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ الجلسة
                            </button>
                            <a href="sessions.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث المواعيد عند اختيار المريض
    document.getElementById('patient_id').addEventListener('change', function() {
        const patientId = this.value;
        const appointmentSelect = document.getElementById('appointment_id');
        
        // إعادة تعيين قائمة المواعيد
        appointmentSelect.innerHTML = '<option value="">اختر الموعد (اختياري)</option>';
        
        if (patientId) {
            // يمكن إضافة AJAX هنا لجلب مواعيد المريض المحدد
            // للآن سنعرض جميع المواعيد
        }
    });
});
</script> 