<?php
/**
 * موديل المواعيد
 * Appointment Model
 */

class Appointment extends Model {
    protected $table = 'appointments';
    
    /**
     * الحصول على المواعيد حسب العيادة
     * Get appointments by clinic
     */
    public function getAppointmentsByClinic($clinicId, $filter = 'all', $date = null) {
        $sql = "SELECT a.*, p.full_name as patient_name, p.phone as patient_phone, p.email as patient_email
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id
                WHERE a.clinic_id = ?";
        
        $params = [$clinicId];
        
        // تطبيق الفلاتر
        if ($filter !== 'all') {
            $sql .= " AND a.status = ?";
            $params[] = $filter;
        }
        
        if ($date) {
            $sql .= " AND a.appointment_date = ?";
            $params[] = $date;
        }
        
        $sql .= " ORDER BY a.appointment_date DESC, a.appointment_time ASC";
        
        return $this->db->query($sql, $params)->fetchAll();
    }
    
    /**
     * الحصول على المواعيد حسب الشهر
     * Get appointments by month
     */
    public function getAppointmentsByMonth($clinicId, $month) {
        $sql = "SELECT a.*, p.full_name as patient_name, p.phone as patient_phone
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id
                WHERE a.clinic_id = ? AND DATE_FORMAT(a.appointment_date, '%Y-%m') = ?
                ORDER BY a.appointment_date ASC, a.appointment_time ASC";
        
        return $this->db->query($sql, [$clinicId, $month])->fetchAll();
    }
    
    /**
     * الحصول على تفاصيل الموعد
     * Get appointment details
     */
    public function getAppointmentDetails($id) {
        $sql = "SELECT a.*, p.full_name as patient_name, p.phone as patient_phone, 
                       p.email as patient_email, p.date_of_birth, p.gender,
                       u.full_name as created_by_name
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id
                LEFT JOIN users u ON a.created_by = u.id
                WHERE a.id = ?";
        
        return $this->db->query($sql, [$id])->fetch();
    }
    
    /**
     * الحصول على مواعيد اليوم
     * Get today's appointments
     */
    public function getTodayAppointments($clinicId) {
        $sql = "SELECT a.*, p.full_name as patient_name, p.phone as patient_phone
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id
                WHERE a.clinic_id = ? AND a.appointment_date = CURDATE()
                ORDER BY a.appointment_time ASC";
        
        return $this->db->query($sql, [$clinicId])->fetchAll();
    }
    
    /**
     * الحصول على المواعيد القادمة
     * Get upcoming appointments
     */
    public function getUpcomingAppointments($clinicId, $limit = 5) {
        $limit = (int) $limit;
        if ($limit <= 0) $limit = 5;
        $sql = "SELECT a.*, p.full_name as patient_name, p.phone as patient_phone
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id
                WHERE a.clinic_id = ? AND a.appointment_date >= CURDATE() AND a.status IN ('pending', 'confirmed')
                ORDER BY a.appointment_date ASC, a.appointment_time ASC
                LIMIT $limit";
        return $this->db->query($sql, [$clinicId])->fetchAll();
    }
    
    /**
     * التحقق من تضارب المواعيد
     * Check for appointment conflicts
     */
    public function hasConflict($clinicId, $date, $time, $duration, $excludeId = null) {
        $endTime = date('H:i', strtotime($time . ' + ' . $duration . ' minutes'));
        
        $sql = "SELECT COUNT(*) as count FROM {$this->table}
                WHERE clinic_id = ? AND appointment_date = ? AND status NOT IN ('cancelled', 'no_show')
                AND (
                    (appointment_time <= ? AND DATE_ADD(CONCAT(appointment_date, ' ', appointment_time), INTERVAL duration MINUTE) > ?) OR
                    (appointment_time < ? AND DATE_ADD(CONCAT(appointment_date, ' ', appointment_time), INTERVAL duration MINUTE) >= ?)
                )";
        
        $params = [$clinicId, $date, $time, $date . ' ' . $time, $date . ' ' . $endTime, $date . ' ' . $endTime];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->query($sql, $params)->fetch();
        return $result['count'] > 0;
    }
    
    /**
     * تحديث حالة الموعد
     * Update appointment status
     */
    public function updateStatus($id, $status) {
        $sql = "UPDATE {$this->table} SET status = ?, updated_at = NOW() WHERE id = ?";
        return $this->db->query($sql, [$status, $id]);
    }
    
    /**
     * الحصول على إحصائيات المواعيد
     * Get appointment statistics
     */
    public function getStats($clinicId) {
        $stats = [];
        
        // إجمالي المواعيد هذا الشهر
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE clinic_id = ? AND MONTH(appointment_date) = MONTH(CURDATE()) AND YEAR(appointment_date) = YEAR(CURDATE())";
        $result = $this->db->query($sql, [$clinicId])->fetch();
        $stats['total_this_month'] = $result['count'];
        
        // المواعيد المؤكدة
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE clinic_id = ? AND status = 'confirmed' AND appointment_date >= CURDATE()";
        $result = $this->db->query($sql, [$clinicId])->fetch();
        $stats['confirmed'] = $result['count'];
        
        // المواعيد المعلقة
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE clinic_id = ? AND status = 'pending' AND appointment_date >= CURDATE()";
        $result = $this->db->query($sql, [$clinicId])->fetch();
        $stats['pending'] = $result['count'];
        
        // مواعيد اليوم
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE clinic_id = ? AND appointment_date = CURDATE()";
        $result = $this->db->query($sql, [$clinicId])->fetch();
        $stats['today'] = $result['count'];
        
        // المواعيد المكتملة هذا الشهر
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE clinic_id = ? AND status = 'completed' AND MONTH(appointment_date) = MONTH(CURDATE()) AND YEAR(appointment_date) = YEAR(CURDATE())";
        $result = $this->db->query($sql, [$clinicId])->fetch();
        $stats['completed_this_month'] = $result['count'];
        
        return $stats;
    }
    
    /**
     * البحث في المواعيد
     * Search appointments
     */
    public function search($clinicId, $query) {
        $sql = "SELECT a.*, p.full_name as patient_name, p.phone as patient_phone
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id
                WHERE a.clinic_id = ? AND (
                    p.full_name LIKE ? OR 
                    p.phone LIKE ? OR 
                    a.notes LIKE ?
                )
                ORDER BY a.appointment_date DESC, a.appointment_time ASC";
        
        $searchTerm = '%' . $query . '%';
        return $this->db->query($sql, [$clinicId, $searchTerm, $searchTerm, $searchTerm])->fetchAll();
    }
    
    /**
     * الحصول على المواعيد حسب المريض
     * Get appointments by patient
     */
    public function getAppointmentsByPatient($patientId) {
        $sql = "SELECT a.*, c.name as clinic_name
                FROM {$this->table} a
                LEFT JOIN clinics c ON a.clinic_id = c.id
                WHERE a.patient_id = ?
                ORDER BY a.appointment_date DESC, a.appointment_time ASC";
        
        return $this->db->query($sql, [$patientId])->fetchAll();
    }
    
    /**
     * إنشاء موعد جديد
     * Create new appointment
     */
    public function create($data) {
        $fields = ['clinic_id', 'patient_id', 'patient_name', 'patient_phone', 'appointment_date', 'appointment_time', 'duration', 'status', 'notes', 'created_by', 'created_at'];
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        
        $sql = "INSERT INTO {$this->table} (" . implode(',', $fields) . ") VALUES ($placeholders)";
        
        $values = [];
        foreach ($fields as $field) {
            $values[] = $data[$field] ?? null;
        }
        
        return $this->db->query($sql, $values);
    }
    
    /**
     * تحديث الموعد
     * Update appointment
     */
    public function update($id, $data) {
        $fields = ['patient_id', 'appointment_date', 'appointment_time', 'duration', 'status', 'notes', 'updated_at'];
        $setClause = implode(' = ?, ', $fields) . ' = ?';
        
        $sql = "UPDATE {$this->table} SET $setClause WHERE id = ?";
        
        $values = [];
        foreach ($fields as $field) {
            $values[] = $data[$field] ?? null;
        }
        $values[] = $id;
        
        return $this->db->query($sql, $values);
    }
    
    /**
     * حذف الموعد
     * Delete appointment
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE id = ?";
        return $this->db->query($sql, [$id]);
    }
    
    /**
     * البحث عن موعد
     * Find appointment
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE id = ?";
        return $this->db->query($sql, [$id])->fetch();
    }
}
?>
