<?php
/**
 * موديل الوصفات الطبية
 * Prescription Model
 */

class Prescription extends Model {
    protected $table = 'prescriptions';
    
    /**
     * الحصول على الوصفات الطبية حسب العيادة
     * Get prescriptions by clinic
     */
    public function getPrescriptionsByClinic($clinicId, $dateFrom = null, $dateTo = null, $status = 'all', $search = '') {
        $sql = "SELECT p.*, pt.full_name as patient_name, pt.phone as patient_phone, pt.email as patient_email
                FROM {$this->table} p
                LEFT JOIN patients pt ON p.patient_id = pt.id
                WHERE p.clinic_id = ?";
        
        $params = [$clinicId];
        
        // تطبيق الفلاتر
        if ($dateFrom && $dateTo) {
            $sql .= " AND p.prescription_date BETWEEN ? AND ?";
            $params[] = $dateFrom;
            $params[] = $dateTo;
        }
        
        if ($status !== 'all') {
            $sql .= " AND p.status = ?";
            $params[] = $status;
        }
        
        if ($search) {
            $sql .= " AND (pt.full_name LIKE ? OR p.prescription_number LIKE ? OR p.diagnosis LIKE ?)";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }
        
        $sql .= " ORDER BY p.prescription_date DESC, p.created_at DESC";
        
        return $this->db->select($sql, $params);
    }
    
    /**
     * الحصول على تفاصيل الوصفة الطبية
     * Get prescription details
     */
    public function getPrescriptionDetails($id) {
        $sql = "SELECT p.*, pt.full_name as patient_name, pt.phone as patient_phone, 
                       pt.email as patient_email, pt.date_of_birth, pt.gender,
                       u.full_name as doctor_name
                FROM {$this->table} p
                LEFT JOIN patients pt ON p.patient_id = pt.id
                LEFT JOIN users u ON p.created_by = u.id
                WHERE p.id = ?";
        
        return $this->db->selectOne($sql, [$id]);
    }
    
    /**
     * الحصول على إحصائيات الوصفات الطبية
     * Get prescription statistics
     */
    public function getStats($clinicId) {
        $today = date('Y-m-d');
        $thisWeek = date('Y-W');
        $thisMonth = date('Y-m');
        
        $stats = [];
        
        // وصفات اليوم
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE clinic_id = ? AND DATE(prescription_date) = ?";
        $result = $this->db->selectOne($sql, [$clinicId, $today]);
        $stats['today'] = $result ? $result['count'] : 0;
        
        // وصفات هذا الأسبوع
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE clinic_id = ? AND YEARWEEK(prescription_date) = ?";
        $result = $this->db->selectOne($sql, [$clinicId, $thisWeek]);
        $stats['this_week'] = $result ? $result['count'] : 0;
        
        // وصفات هذا الشهر
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE clinic_id = ? AND DATE_FORMAT(prescription_date, '%Y-%m') = ?";
        $result = $this->db->selectOne($sql, [$clinicId, $thisMonth]);
        $stats['this_month'] = $result ? $result['count'] : 0;
        
        // إجمالي الأدوية
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE clinic_id = ?";
        $result = $this->db->selectOne($sql, [$clinicId]);
        $stats['total_medications'] = $result ? $result['count'] : 0;
        
        return $stats;
    }
    
    /**
     * الحصول على آخر رقم وصفة طبية
     * Get last prescription number
     */
    public function getLastPrescriptionNumber($year, $month) {
        $sql = "SELECT prescription_number FROM {$this->table} 
                WHERE prescription_number LIKE ? 
                ORDER BY prescription_number DESC 
                LIMIT 1";
        
        $pattern = "PRESC-{$year}{$month}-%";
        $result = $this->db->selectOne($sql, [$pattern]);
        
        return $result ? $result['prescription_number'] : null;
    }
    
    /**
     * إنشاء وصفة طبية جديدة
     * Create new prescription
     */
    public function create($data) {
        $fields = ['clinic_id', 'patient_id', 'prescription_number', 'prescription_date', 'diagnosis', 
                   'medications', 'dosage_instructions', 'duration', 'notes', 'status', 'created_by', 'created_at'];
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        
        $sql = "INSERT INTO {$this->table} (" . implode(',', $fields) . ") VALUES ($placeholders)";
        
        $values = [];
        foreach ($fields as $field) {
            $values[] = $data[$field] ?? null;
        }
        
        return $this->db->query($sql, $values);
    }
    
    /**
     * تحديث الوصفة الطبية
     * Update prescription
     */
    public function update($id, $data) {
        $fields = ['patient_id', 'prescription_date', 'diagnosis', 'medications', 
                   'dosage_instructions', 'duration', 'notes', 'status', 'updated_at'];
        $setClause = implode(' = ?, ', $fields) . ' = ?';
        
        $sql = "UPDATE {$this->table} SET $setClause WHERE id = ?";
        
        $values = [];
        foreach ($fields as $field) {
            $values[] = $data[$field] ?? null;
        }
        $values[] = $id;
        
        return $this->db->query($sql, $values);
    }
    
    /**
     * حذف الوصفة الطبية
     * Delete prescription
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE id = ?";
        return $this->db->query($sql, [$id]);
    }
    
    /**
     * البحث عن وصفة طبية
     * Find prescription
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE id = ?";
        return $this->db->selectOne($sql, [$id]);
    }
    
    /**
     * الحصول على الوصفات الطبية النشطة للمريض
     * Get active prescriptions for patient
     */
    public function getActivePrescriptionsForPatient($patientId) {
        $sql = "SELECT * FROM {$this->table} WHERE patient_id = ? AND status = 'active' ORDER BY prescription_date DESC";
        return $this->db->select($sql, [$patientId]);
    }
} 