<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            مرحباً بك، <?= $user['full_name'] ?>
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">إجمالي المرضى</h6>
                    <div class="stats-number"><?= $stats['patients_count'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-users fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">مواعيد هذا الشهر</h6>
                    <div class="stats-number"><?= $stats['appointments_count'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-calendar-alt fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">الدخل الشهري</h6>
                    <div class="stats-number"><?= number_format($stats['monthly_income'] ?? 0) ?> ₪</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-money-bill-wave fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">مواعيد معلقة</h6>
                    <div class="stats-number"><?= $stats['pending_appointments'] ?? 0 ?></div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Today's Appointments -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-day me-2"></i>
                    مواعيد اليوم
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($today_appointments)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مواعيد اليوم</p>
                        <a href="appointments.php" class="btn btn-primary">إضافة موعد جديد</a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>اسم المريض</th>
                                    <th>الهاتف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($today_appointments as $appointment): ?>
                                <tr>
                                    <td>
                                        <strong><?= date('H:i', strtotime($appointment['appointment_time'])) ?></strong>
                                    </td>
                                    <td>
                                        <?= $appointment['patient_name'] ?: $appointment['patient_name'] ?>
                                    </td>
                                    <td><?= $appointment['patient_phone'] ?></td>
                                    <td>
                                        <?php
                                        $statusClass = [
                                            'pending' => 'warning',
                                            'confirmed' => 'info',
                                            'completed' => 'success',
                                            'cancelled' => 'danger',
                                            'no_show' => 'secondary'
                                        ];
                                        $statusText = [
                                            'pending' => 'معلق',
                                            'confirmed' => 'مؤكد',
                                            'completed' => 'مكتمل',
                                            'cancelled' => 'ملغي',
                                            'no_show' => 'لم يحضر'
                                        ];
                                        ?>
                                        <span class="badge bg-<?= $statusClass[$appointment['status']] ?>">
                                            <?= $statusText[$appointment['status']] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <?php if ($appointment['status'] == 'pending'): ?>
                                                <button class="btn btn-success btn-sm" onclick="markAsPaid(<?= $appointment['id'] ?>)">
                                                    <i class="fas fa-check"></i> تم الدفع
                                                </button>
                                                <button class="btn btn-warning btn-sm" onclick="markAsDebt(<?= $appointment['id'] ?>)">
                                                    <i class="fas fa-exclamation-triangle"></i> دين
                                                </button>
                                                <button class="btn btn-primary btn-sm" onclick="addToSession(<?= $appointment['id'] ?>)">
                                                    <i class="fas fa-plus"></i> إضافة للجلسة
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions & Notifications -->
    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="appointments.php?action=new" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        موعد جديد
                    </a>
                    <a href="patients.php?action=new" class="btn btn-success">
                        <i class="fas fa-user-plus me-2"></i>
                        مريض جديد
                    </a>
                    <a href="sessions.php?action=new" class="btn btn-info">
                        <i class="fas fa-stethoscope me-2"></i>
                        جلسة طبية
                    </a>
                    <a href="prescriptions.php?action=new" class="btn btn-warning">
                        <i class="fas fa-prescription-bottle-alt me-2"></i>
                        وصفة طبية
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Recent Notifications -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bell me-2"></i>
                    الإشعارات الأخيرة
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($notifications)): ?>
                    <p class="text-muted text-center">لا توجد إشعارات جديدة</p>
                <?php else: ?>
                    <?php foreach ($notifications as $notification): ?>
                    <div class="notification-item border-bottom pb-2 mb-2">
                        <h6 class="mb-1"><?= $notification['title'] ?></h6>
                        <p class="mb-1 small text-muted"><?= $notification['message'] ?></p>
                        <small class="text-muted">
                            <?= date('H:i', strtotime($notification['created_at'])) ?>
                        </small>
                    </div>
                    <?php endforeach; ?>
                    <div class="text-center mt-3">
                        <a href="notifications.php" class="btn btn-sm btn-outline-primary">
                            عرض جميع الإشعارات
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Clinic Info -->
<?php if ($clinic): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clinic-medical me-2"></i>
                    معلومات العيادة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>اسم العيادة:</strong> <?= $clinic['name'] ?></p>
                        <p><strong>التخصص:</strong> <?= $clinic['specialization'] ?></p>
                        <p><strong>الهاتف:</strong> <?= $clinic['phone'] ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>خطة الاشتراك:</strong> 
                            <?php
                            $planNames = [
                                'trial' => 'النسخة التجريبية',
                                'basic' => 'الخطة الأساسية',
                                'pro' => 'الخطة المتقدمة',
                                'enterprise' => 'الخطة المؤسسية'
                            ];
                            $plan = $clinic['subscription_plan'] ?? 'trial';
                            ?>
                            <span class="badge bg-info"><?= $planNames[$plan] ?? ucfirst($plan) ?></span>
                        </p>
                        <p><strong>تاريخ انتهاء الاشتراك:</strong> 
                            <?= date('Y-m-d', strtotime($clinic['subscription_end'])) ?>
                        </p>
                        <?php
                        $daysLeft = ceil((strtotime($clinic['subscription_end']) - time()) / (24 * 60 * 60));
                        ?>
                        <p><strong>الأيام المتبقية:</strong> 
                            <span class="badge bg-<?= $daysLeft > 7 ? 'success' : ($daysLeft > 3 ? 'warning' : 'danger') ?>">
                                <?= $daysLeft ?> يوم
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function markAsPaid(appointmentId) {
    if (confirm('هل أنت متأكد من تأكيد الدفع؟')) {
        // AJAX call to update appointment status
        fetch('api/appointments.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'mark_paid',
                appointment_id: appointmentId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }
}

function markAsDebt(appointmentId) {
    if (confirm('هل أنت متأكد من تسجيل هذا كدين؟')) {
        // AJAX call to update appointment status
        fetch('api/appointments.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'mark_debt',
                appointment_id: appointmentId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }
}

function addToSession(appointmentId) {
    // Redirect to session creation page
    window.location.href = 'sessions.php?action=new&appointment_id=' + appointmentId;
}
</script>
