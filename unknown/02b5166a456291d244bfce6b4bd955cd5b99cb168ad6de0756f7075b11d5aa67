<?php
/**
 * صفحة التقارير
 * Reports Page
 */

// تشغيل الجلسات
session_start();

// تعريف المسارات
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('VIEWS_PATH', APP_PATH . '/views');

// تحميل الإعدادات
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/models/Patient.php';
require_once APP_PATH . '/models/Appointment.php';
require_once APP_PATH . '/models/MedicalSession.php';
require_once APP_PATH . '/controllers/ReportsController.php';
require_once APP_PATH . '/core/AuthGuard.php';
requireAuth();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// تشغيل كونترولر التقارير
$controller = new ReportsController();
$action = $_GET['action'] ?? 'index';

switch ($action) {
    case 'index':
        $controller->index();
        break;
    case 'patients':
        $controller->patients();
        break;
    case 'appointments':
        $controller->appointments();
        break;
    case 'sessions':
        $controller->sessions();
        break;
    case 'income':
        $controller->income();
        break;
    case 'export':
        $controller->export();
        break;
    default:
        $controller->index();
        break;
}
?>
