<?php
/**
 * صفحة إدارة الرسائل
 * Messages Management Page
 */

// تشغيل الجلسات
session_start();

// تعريف المسارات
define('ROOT_PATH', __DIR__ . '/..');
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('VIEWS_PATH', APP_PATH . '/views');

// تحميل الإعدادات
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/core/AuthGuard.php';
require_once APP_PATH . '/models/ContactMessage.php';
require_once APP_PATH . '/controllers/Admin/DashboardController.php';

// التحقق من وجود الدوال المطلوبة
if (!function_exists('adminOnly')) {
    die('خطأ: دالة adminOnly غير موجودة');
}

// تشغيل صفحة الرسائل
try {
    $controller = new DashboardController();
    $controller->messages();
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
    echo "<br>الملف: " . $e->getFile();
    echo "<br>السطر: " . $e->getLine();
}
?> 