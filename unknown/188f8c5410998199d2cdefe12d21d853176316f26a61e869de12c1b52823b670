<?php
/**
 * صفحة إضافة مريض جديد
 * Add New Patient Page
 */
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="fas fa-user-plus me-2 text-primary"></i>
        إضافة مريض جديد
    </h2>
    <a href="patients.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للقائمة
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    البيانات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="patientForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="phone" name="phone" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="national_id" class="form-label">رقم الهوية الوطنية</label>
                            <input type="text" class="form-control" id="national_id" name="national_id">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="gender" class="form-label">الجنس <span class="text-danger">*</span></label>
                            <select class="form-select" id="gender" name="gender" required>
                                <option value="">اختر الجنس</option>
                                <option value="male">ذكر</option>
                                <option value="female">أنثى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="2" 
                                  placeholder="العنوان الكامل..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="emergency_contact" class="form-label">جهة الاتصال في حالات الطوارئ</label>
                        <input type="text" class="form-control" id="emergency_contact" name="emergency_contact" 
                               placeholder="الاسم ورقم الهاتف">
                    </div>
                    
                    <hr class="my-4">
                    
                    <h6 class="mb-3">
                        <i class="fas fa-heartbeat me-2 text-danger"></i>
                        المعلومات الطبية
                    </h6>
                    
                    <div class="mb-3">
                        <label for="medical_history" class="form-label">التاريخ المرضي</label>
                        <textarea class="form-control" id="medical_history" name="medical_history" rows="3" 
                                  placeholder="الأمراض السابقة، العمليات الجراحية، الأدوية المستمرة..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="allergies" class="form-label">الحساسية</label>
                        <textarea class="form-control" id="allergies" name="allergies" rows="2" 
                                  placeholder="حساسية الأدوية، الطعام، أو أي مواد أخرى..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2" 
                                  placeholder="أي ملاحظات مهمة أخرى..."></textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="patients.php" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ المريض
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- نصائح -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح مهمة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من صحة رقم الهاتف
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        أدخل التاريخ المرضي بدقة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        لا تنس تسجيل الحساسية
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        يمكنك تعديل البيانات لاحقاً
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- معلومات الخصوصية -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    الخصوصية والأمان
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted small mb-2">
                    <i class="fas fa-lock me-1"></i>
                    جميع البيانات محمية ومشفرة
                </p>
                <p class="text-muted small mb-2">
                    <i class="fas fa-user-shield me-1"></i>
                    لا يتم مشاركة البيانات مع أطراف خارجية
                </p>
                <p class="text-muted small mb-0">
                    <i class="fas fa-history me-1"></i>
                    يتم حفظ سجل التعديلات
                </p>
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    بعد الحفظ
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted small mb-3">
                    بعد حفظ بيانات المريض يمكنك:
                </p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-info btn-sm" disabled>
                        <i class="fas fa-calendar-plus me-2"></i>حجز موعد
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" disabled>
                        <i class="fas fa-stethoscope me-2"></i>بدء جلسة طبية
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('patientForm');
    const phoneInput = document.getElementById('phone');
    const emailInput = document.getElementById('email');
    const nameInput = document.getElementById('name');
    
    // تنسيق رقم الهاتف
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 0) {
            if (value.startsWith('966')) {
                value = '+' + value;
            } else if (value.startsWith('05')) {
                value = '+966' + value.substring(1);
            } else if (value.startsWith('5')) {
                value = '+966' + value;
            }
        }
        this.value = value;
    });
    
    // تحويل الاسم للأحرف الكبيرة
    nameInput.addEventListener('blur', function() {
        this.value = this.value.replace(/\b\w/g, l => l.toUpperCase());
    });
    
    // التحقق من صحة النموذج
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
    
    // تحسين تجربة المستخدم
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.borderColor = 'var(--primary-blue)';
        });
        
        input.addEventListener('blur', function() {
            this.style.borderColor = '';
        });
    });
});

function validateForm() {
    const name = document.getElementById('name').value.trim();
    const phone = document.getElementById('phone').value.trim();
    const gender = document.getElementById('gender').value;
    const email = document.getElementById('email').value.trim();
    
    if (!name) {
        alert('يرجى إدخال اسم المريض');
        document.getElementById('name').focus();
        return false;
    }
    
    if (name.length < 3) {
        alert('يجب أن يكون الاسم 3 أحرف على الأقل');
        document.getElementById('name').focus();
        return false;
    }
    
    if (!phone) {
        alert('يرجى إدخال رقم الهاتف');
        document.getElementById('phone').focus();
        return false;
    }
    
    if (!gender) {
        alert('يرجى اختيار الجنس');
        document.getElementById('gender').focus();
        return false;
    }
    
    // التحقق من صحة البريد الإلكتروني
    if (email && !isValidEmail(email)) {
        alert('يرجى إدخال بريد إلكتروني صحيح');
        document.getElementById('email').focus();
        return false;
    }
    
    return true;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// إضافة تأثيرات بصرية
document.querySelectorAll('.card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = 'var(--shadow-hover)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = '';
        this.style.boxShadow = '';
    });
});
</script>
