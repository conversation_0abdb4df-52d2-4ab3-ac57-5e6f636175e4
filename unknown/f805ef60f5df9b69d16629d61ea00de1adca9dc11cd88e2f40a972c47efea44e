<?php
/**
 * موديل المرضى
 * Patient Model
 */

class Patient extends Model {
    protected $table = 'patients';
    
    /**
     * الحصول على المرضى حسب العيادة
     * Get patients by clinic
     */
    public function getPatientsByClinic($clinicId) {
        $sql = "SELECT * FROM {$this->table} WHERE clinic_id = ? ORDER BY full_name ASC";
        return $this->db->query($sql, [$clinicId])->fetchAll();
    }
    
    /**
     * البحث في المرضى
     * Search patients
     */
    public function search($clinicId, $query) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE clinic_id = ? AND (
                    full_name LIKE ? OR 
                    phone LIKE ? OR 
                    email LIKE ? OR
                    national_id LIKE ?
                )
                ORDER BY full_name ASC";
        
        $searchTerm = '%' . $query . '%';
        return $this->db->query($sql, [$clinicId, $searchTerm, $searchTerm, $searchTerm, $searchTerm])->fetchAll();
    }
    
    /**
     * الحصول على إحصائيات المرضى
     * Get patient statistics
     */
    public function getStats($clinicId) {
        $stats = [];
        
        // إجمالي المرضى
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE clinic_id = ?";
        $result = $this->db->query($sql, [$clinicId])->fetch();
        $stats['total'] = $result['count'];
        
        // المرضى الجدد هذا الشهر
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE clinic_id = ? AND MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())";
        $result = $this->db->query($sql, [$clinicId])->fetch();
        $stats['new_this_month'] = $result['count'];
        
        // المرضى الذكور
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE clinic_id = ? AND gender = 'male'";
        $result = $this->db->query($sql, [$clinicId])->fetch();
        $stats['male'] = $result['count'];
        
        // المرضى الإناث
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE clinic_id = ? AND gender = 'female'";
        $result = $this->db->query($sql, [$clinicId])->fetch();
        $stats['female'] = $result['count'];
        
        return $stats;
    }
    
    /**
     * إنشاء مريض جديد
     * Create new patient
     */
    public function create($data) {
        $fields = ['clinic_id', 'full_name', 'phone', 'email', 'date_of_birth', 'gender', 'national_id', 'address', 'medical_history', 'allergies', 'emergency_contact', 'notes', 'created_at'];
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        
        $sql = "INSERT INTO {$this->table} (" . implode(',', $fields) . ") VALUES ($placeholders)";
        
        $values = [];
        foreach ($fields as $field) {
            $values[] = $data[$field] ?? null;
        }
        
        return $this->db->query($sql, $values);
    }
    
    /**
     * تحديث المريض
     * Update patient
     */
    public function update($id, $data) {
        $fields = ['full_name', 'phone', 'email', 'date_of_birth', 'gender', 'national_id', 'address', 'medical_history', 'allergies', 'emergency_contact', 'notes', 'updated_at'];
        $setClause = implode(' = ?, ', $fields) . ' = ?';
        
        $sql = "UPDATE {$this->table} SET $setClause WHERE id = ?";
        
        $values = [];
        foreach ($fields as $field) {
            $values[] = $data[$field] ?? null;
        }
        $values[] = $id;
        
        return $this->db->query($sql, $values);
    }
    
    /**
     * حذف المريض
     * Delete patient
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE id = ?";
        return $this->db->query($sql, [$id]);
    }
    
    /**
     * البحث عن مريض
     * Find patient
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE id = ?";
        return $this->db->query($sql, [$id])->fetch();
    }
    
    /**
     * التحقق من وجود رقم الهاتف
     * Check if phone exists
     */
    public function phoneExists($phone, $clinicId, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE phone = ? AND clinic_id = ?";
        $params = [$phone, $clinicId];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->query($sql, $params)->fetch();
        return $result['count'] > 0;
    }
    
    /**
     * التحقق من وجود البريد الإلكتروني
     * Check if email exists
     */
    public function emailExists($email, $clinicId, $excludeId = null) {
        if (empty($email)) return false;
        
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE email = ? AND clinic_id = ?";
        $params = [$email, $clinicId];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->query($sql, $params)->fetch();
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على تاريخ المريض الطبي
     * Get patient medical history
     */
    public function getMedicalHistory($patientId) {
        // الحصول على المواعيد والجلسات الطبية
        $sql = "SELECT 
                    'appointment' as type,
                    a.appointment_date as date,
                    a.appointment_time as time,
                    a.notes,
                    a.status,
                    NULL as diagnosis,
                    NULL as treatment
                FROM appointments a
                WHERE a.patient_id = ?
                
                UNION ALL
                
                SELECT 
                    'session' as type,
                    s.session_date as date,
                    s.session_time as time,
                    s.notes,
                    s.status,
                    s.diagnosis,
                    s.treatment
                FROM medical_sessions s
                WHERE s.patient_id = ?
                
                ORDER BY date DESC, time DESC";
        
        return $this->db->query($sql, [$patientId, $patientId])->fetchAll();
    }
    
    /**
     * الحصول على آخر زيارة للمريض
     * Get patient's last visit
     */
    public function getLastVisit($patientId) {
        $sql = "SELECT appointment_date, appointment_time, status
                FROM appointments
                WHERE patient_id = ? AND status = 'completed'
                ORDER BY appointment_date DESC, appointment_time DESC
                LIMIT 1";
        
        return $this->db->query($sql, [$patientId])->fetch();
    }
    
    /**
     * الحصول على الموعد القادم للمريض
     * Get patient's next appointment
     */
    public function getNextAppointment($patientId) {
        $sql = "SELECT appointment_date, appointment_time, status
                FROM appointments
                WHERE patient_id = ? AND appointment_date >= CURDATE() AND status IN ('pending', 'confirmed')
                ORDER BY appointment_date ASC, appointment_time ASC
                LIMIT 1";
        
        return $this->db->query($sql, [$patientId])->fetch();
    }
    
    /**
     * حساب عمر المريض
     * Calculate patient age
     */
    public function calculateAge($dateOfBirth) {
        if (empty($dateOfBirth)) return null;
        
        $birthDate = new DateTime($dateOfBirth);
        $today = new DateTime();
        $age = $today->diff($birthDate);
        
        return $age->y;
    }
    
    /**
     * الحصول على المرضى الأكثر زيارة
     * Get most frequent patients
     */
    public function getMostFrequentPatients($clinicId, $limit = 10) {
        $limit = (int) $limit;
        if ($limit <= 0) $limit = 10;
        $sql = "SELECT p.*, COUNT(a.id) as visit_count
                FROM {$this->table} p
                LEFT JOIN appointments a ON p.id = a.patient_id AND a.status = 'completed'
                WHERE p.clinic_id = ?
                GROUP BY p.id
                ORDER BY visit_count DESC, p.full_name ASC
                LIMIT $limit";
        return $this->db->query($sql, [$clinicId])->fetchAll();
    }
    
    /**
     * الحصول على المرضى الجدد
     * Get new patients
     */
    public function getNewPatients($clinicId, $days = 30) {
        $sql = "SELECT * FROM {$this->table}
                WHERE clinic_id = ? AND created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY created_at DESC";
        
        return $this->db->query($sql, [$clinicId, $days])->fetchAll();
    }
}
?>
