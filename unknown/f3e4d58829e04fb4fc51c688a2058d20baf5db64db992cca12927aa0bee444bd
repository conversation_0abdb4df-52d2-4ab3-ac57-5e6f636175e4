<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'نظام حكيم' ?></title>
    <meta name="description" content="<?= $description ?? 'نظام شامل لإدارة العيادات الطبية' ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%23007BFF'/><path d='M30 25c-8 0-15 7-15 15v10c0 3 2 5 5 5s5-2 5-5v-10c0-3 2-5 5-5s5 2 5 5v25c0 8 7 15 15 15s15-7 15-15v-25c0-3 2-5 5-5s5 2 5 5v10c0 3 2 5 5 5s5-2 5-5v-10c0-8-7-15-15-15-3 0-6 1-8 3-2-2-5-3-8-3z' fill='white'/><circle cx='50' cy='75' r='8' fill='white'/></svg>">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%23007BFF'/><path d='M30 25c-8 0-15 7-15 15v10c0 3 2 5 5 5s5-2 5-5v-10c0-3 2-5 5-5s5 2 5 5v25c0 8 7 15 15 15s15-7 15-15v-25c0-3 2-5 5-5s5 2 5 5v10c0 3 2 5 5 5s5-2 5-5v-10c0-8-7-15-15-15-3 0-6 1-8 3-2-2-5-3-8-3z' fill='white'/><circle cx='50' cy='75' r='8' fill='white'/></svg>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Cairo Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #007BFF;
            --secondary-color: #FFFFFF;
            --accent-color: #28A745;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --border-color: #e9ecef;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
            color: var(--text-dark);
            line-height: 1.6;
        }
        
        .navbar {
            background: var(--secondary-color);
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            padding: 0.8rem 0;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            padding: 0.6rem 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.7rem;
            color: var(--primary-color) !important;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .navbar-brand:hover {
            transform: scale(1.02);
        }

        .navbar-brand i {
            margin-left: 0.6rem;
            color: var(--accent-color);
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover i {
            transform: rotate(360deg);
        }

        .nav-link {
            font-weight: 500;
            font-size: 1rem;
            color: var(--text-dark) !important;
            margin: 0 0.3rem;
            padding: 0.7rem 1rem !important;
            border-radius: 20px;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
            background: rgba(0, 123, 255, 0.08);
            transform: translateY(-1px);
        }

        .nav-link i {
            margin-left: 0.6rem;
            font-size: 0.9rem;
            width: 16px;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            border-color: var(--primary-color);
            font-weight: 600;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            border: none;
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0056b3, var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
            color: white;
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
            font-weight: 600;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--accent-color), #20c997);
            border-color: var(--accent-color);
            font-weight: 600;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
            border: none;
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #20c997, var(--accent-color));
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
            color: white;
        }

        .btn-outline-light {
            border: 2px solid rgba(255,255,255,0.8);
            color: rgba(255,255,255,0.9);
            background: transparent;
            font-weight: 600;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            transition: all 0.3s ease;
        }

        .btn-outline-light:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255,255,255,0.2);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            font-weight: 600;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
        }

        .navbar .btn {
            font-size: 0.95rem;
            font-weight: 500;
            padding: 0.6rem 1.3rem;
            margin: 0 0.3rem;
            border-radius: 20px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            border-width: 1.5px;
        }

        .navbar .btn i {
            margin-left: 0.5rem;
            font-size: 0.85rem;
        }

        .navbar .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background: transparent;
        }

        .navbar .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0, 123, 255, 0.3);
        }

        .navbar .btn-success {
            background: var(--accent-color);
            border-color: var(--accent-color);
            color: white;
        }

        .navbar .btn-success:hover {
            background: #1e7e34;
            border-color: #1e7e34;
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(40, 167, 69, 0.3);
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 5rem 0;
            min-height: 80vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
            background-position: bottom;
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.95;
            position: relative;
            z-index: 2;
            line-height: 1.6;
            font-weight: 400;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            .hero-subtitle {
                font-size: 1.1rem;
            }

            .navbar .btn {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
                margin: 0.2rem;
            }

            .navbar-nav {
                text-align: center;
                margin-top: 1rem;
            }

            .navbar-nav .nav-link {
                margin: 0.2rem 0;
                padding: 0.6rem 1rem !important;
            }

            .d-flex.align-items-center {
                flex-direction: row;
                justify-content: center;
                gap: 0.5rem;
                margin-top: 1rem;
            }

            .d-flex.align-items-center .btn {
                flex: 1;
                max-width: 140px;
            }

            .pricing-card-new {
                margin-bottom: 2rem;
            }

            .pricing-card-enhanced {
                margin-bottom: 2rem;
            }
        }

        /* Enhanced Pricing Cards - Better Design */
        .pricing-card-enhanced {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .pricing-card-enhanced:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,123,255,0.15);
            border-color: var(--primary-color);
        }

        .pricing-card-enhanced.popular {
            border-color: var(--accent-color);
            transform: scale(1.05);
        }

        .pricing-card-enhanced.popular:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 25px 50px rgba(40, 167, 69, 0.2);
        }

        .popular-badge-enhanced {
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, var(--accent-color), #20c997);
            color: white;
            padding: 0.6rem 1.5rem;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            z-index: 2;
        }

        .pricing-header-enhanced {
            text-align: center;
            padding: 2rem 2rem 1rem;
        }

        .pricing-icon-enhanced {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            margin: 0 auto 1.5rem;
            transition: all 0.3s ease;
        }

        .pricing-card-enhanced.popular .pricing-icon-enhanced {
            background: linear-gradient(135deg, var(--accent-color), #20c997);
        }

        .pricing-card-enhanced:hover .pricing-icon-enhanced {
            transform: scale(1.1);
        }

        .pricing-title-enhanced {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .pricing-subtitle-enhanced {
            font-size: 0.95rem;
            color: #6c757d;
            margin-bottom: 0;
        }

        .pricing-price-enhanced {
            text-align: center;
            padding: 1.5rem 2rem;
            background: rgba(0,123,255,0.05);
            margin: 0 1rem;
            border-radius: 15px;
        }

        .pricing-card-enhanced.popular .pricing-price-enhanced {
            background: rgba(40, 167, 69, 0.05);
        }

        .price-container {
            margin-bottom: 0.5rem;
        }

        .price-container .currency {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            vertical-align: top;
        }

        .price-container .amount {
            font-size: 2.8rem;
            font-weight: 800;
            color: var(--primary-color);
            margin: 0 0.2rem;
        }

        .price-container .period {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
        }

        .price-note {
            font-size: 0.8rem;
            color: var(--accent-color);
            font-weight: 600;
        }

        .pricing-features-enhanced {
            padding: 2rem;
        }

        .pricing-features-enhanced ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .pricing-features-enhanced li {
            display: flex;
            align-items: center;
            padding: 0.7rem 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        .pricing-features-enhanced li:last-child {
            border-bottom: none;
        }

        .pricing-features-enhanced li i {
            color: var(--accent-color);
            margin-left: 1rem;
            font-size: 1rem;
            width: 16px;
        }

        .pricing-features-enhanced li span {
            font-size: 0.95rem;
            color: #495057;
        }

        .pricing-footer-enhanced {
            padding: 0 2rem 2rem;
        }

        .pricing-btn-enhanced {
            display: block;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-align: center;
            box-shadow: 0 6px 20px rgba(0,123,255,0.3);
        }

        .pricing-btn-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,123,255,0.4);
            color: white;
        }

        .pricing-btn-enhanced.featured {
            background: linear-gradient(135deg, var(--accent-color), #20c997);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
        }

        .pricing-btn-enhanced.featured:hover {
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        /* Guarantee Badge */
        .guarantee-badge {
            display: inline-flex;
            align-items: center;
            background: rgba(40, 167, 69, 0.1);
            color: var(--accent-color);
            padding: 0.7rem 1.5rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        /* Modern Pricing Cards - Revolutionary Design */
        .pricing-card-modern {
            background: white;
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            transition: all 0.5s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(0,123,255,0.1);
        }

        .pricing-card-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .pricing-card-modern:hover::before {
            opacity: 1;
        }

        .pricing-card-modern:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 30px 80px rgba(0,123,255,0.2);
        }

        .pricing-card-modern.popular {
            border-color: var(--accent-color);
            transform: scale(1.05);
        }

        .pricing-card-modern.popular::before {
            opacity: 1;
            background: linear-gradient(90deg, var(--accent-color), #20c997);
        }

        .pricing-card-modern.popular:hover {
            transform: translateY(-15px) scale(1.07);
            box-shadow: 0 35px 90px rgba(40, 167, 69, 0.25);
        }

        /* Popular Ribbon */
        .popular-ribbon {
            position: absolute;
            top: 20px;
            right: -35px;
            background: linear-gradient(135deg, var(--accent-color), #20c997);
            color: white;
            padding: 0.5rem 3rem;
            transform: rotate(45deg);
            font-size: 0.8rem;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            z-index: 2;
        }

        /* Header Section */
        .pricing-header-modern {
            text-align: center;
            padding: 2.5rem 2rem 1.5rem;
            position: relative;
        }

        .pricing-badge-modern {
            display: inline-block;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 0.4rem 1.2rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 1.5rem;
        }

        .pricing-badge-modern.featured {
            background: linear-gradient(135deg, var(--accent-color), #20c997);
        }

        .pricing-badge-modern.enterprise {
            background: linear-gradient(135deg, #6f42c1, #495057);
        }

        .pricing-icon-modern {
            margin-bottom: 1.5rem;
        }

        .icon-bg {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, rgba(0,123,255,0.1), rgba(0,123,255,0.2));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            position: relative;
            transition: all 0.4s ease;
        }

        .icon-bg::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .pricing-card-modern:hover .icon-bg::before {
            opacity: 1;
        }

        .icon-bg.featured {
            background: linear-gradient(135deg, rgba(40,167,69,0.1), rgba(40,167,69,0.2));
        }

        .icon-bg.enterprise {
            background: linear-gradient(135deg, rgba(111,66,193,0.1), rgba(111,66,193,0.2));
        }

        .icon-bg i {
            font-size: 2rem;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .icon-bg.featured i {
            color: var(--accent-color);
        }

        .icon-bg.enterprise i {
            color: #6f42c1;
        }

        .pricing-card-modern:hover .icon-bg {
            transform: scale(1.1) rotateY(360deg);
        }

        .pricing-card-modern:hover .icon-bg i {
            color: white;
        }

        .pricing-title-modern {
            font-size: 1.6rem;
            font-weight: 800;
            color: var(--text-dark);
            margin-bottom: 0.8rem;
        }

        .pricing-desc-modern {
            font-size: 1rem;
            color: #6c757d;
            line-height: 1.5;
            margin-bottom: 0;
        }

        /* Price Section */
        .pricing-price-modern {
            text-align: center;
            padding: 2rem;
            background: linear-gradient(135deg, rgba(0,123,255,0.05), rgba(0,123,255,0.1));
            margin: 0 1.5rem;
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .pricing-card-modern.popular .pricing-price-modern {
            background: linear-gradient(135deg, rgba(40,167,69,0.05), rgba(40,167,69,0.1));
        }

        .pricing-price-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .pricing-card-modern:hover .pricing-price-modern::before {
            left: 100%;
        }

        .price-main {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.8rem;
        }

        .price-number {
            font-size: 3.5rem;
            font-weight: 900;
            color: var(--primary-color);
            line-height: 1;
        }

        .pricing-card-modern.popular .price-number {
            color: var(--accent-color);
        }

        .price-details {
            display: flex;
            flex-direction: column;
            margin-right: 0.5rem;
            align-items: flex-start;
        }

        .currency {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .pricing-card-modern.popular .currency {
            color: var(--accent-color);
        }

        .period {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
        }

        .price-offer {
            background: rgba(40, 167, 69, 0.1);
            color: var(--accent-color);
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        .price-offer.featured {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
            border-color: rgba(255, 193, 7, 0.3);
        }

        /* Features Section */
        .pricing-features-modern {
            padding: 2rem;
        }

        .feature-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .feature-item:last-child {
            border-bottom: none;
        }

        .feature-item:hover {
            background: rgba(0,123,255,0.02);
            padding-left: 0.5rem;
            border-radius: 10px;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, rgba(0,123,255,0.1), rgba(0,123,255,0.2));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .feature-item:hover .feature-icon {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            transform: scale(1.1);
        }

        .feature-icon i {
            font-size: 1rem;
            color: var(--primary-color);
            transition: color 0.3s ease;
        }

        .feature-item:hover .feature-icon i {
            color: white;
        }

        .feature-text {
            flex: 1;
        }

        .feature-text strong {
            display: block;
            font-size: 1rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.3rem;
        }

        .feature-text span {
            font-size: 0.85rem;
            color: #6c757d;
            line-height: 1.4;
        }

        /* Centered Features */
        .feature-item-centered {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 1.5rem 1rem;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .feature-item-centered:last-child {
            border-bottom: none;
        }

        .feature-item-centered:hover {
            background: rgba(0,123,255,0.03);
            border-radius: 15px;
            transform: translateY(-2px);
        }

        .feature-icon-centered {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, rgba(0,123,255,0.1), rgba(0,123,255,0.2));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .feature-item-centered:hover .feature-icon-centered {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            transform: scale(1.1);
        }

        .feature-icon-centered i {
            font-size: 1.2rem;
            color: var(--primary-color);
            transition: color 0.3s ease;
        }

        .feature-item-centered:hover .feature-icon-centered i {
            color: white;
        }

        .feature-text-centered {
            width: 100%;
        }

        .feature-text-centered strong {
            display: block;
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .feature-text-centered span {
            font-size: 0.9rem;
            color: #6c757d;
            line-height: 1.5;
        }

        /* Action Section */
        .pricing-action-modern {
            padding: 0 2rem 2.5rem;
        }

        .btn-modern {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 1.2rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            border: none;
            gap: 0.8rem;
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .btn-modern:hover::before {
            left: 100%;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            box-shadow: 0 8px 25px rgba(0,123,255,0.3);
        }

        .btn-primary-modern:hover {
            background: linear-gradient(135deg, #0056b3, var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0,123,255,0.4);
            color: white;
        }

        .btn-featured-modern {
            background: linear-gradient(135deg, var(--accent-color), #20c997);
            color: white;
            box-shadow: 0 8px 25px rgba(40,167,69,0.3);
        }

        .btn-featured-modern:hover {
            background: linear-gradient(135deg, #20c997, var(--accent-color));
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(40,167,69,0.4);
            color: white;
        }

        .btn-enterprise-modern {
            background: linear-gradient(135deg, #6f42c1, #495057);
            color: white;
            box-shadow: 0 8px 25px rgba(111,66,193,0.3);
        }

        .btn-enterprise-modern:hover {
            background: linear-gradient(135deg, #495057, #6f42c1);
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(111,66,193,0.4);
            color: white;
        }

        .btn-modern i {
            transition: transform 0.3s ease;
        }

        .btn-modern:hover i {
            transform: translateX(-5px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .pricing-card-modern {
                margin-bottom: 2rem;
            }

            .pricing-card-modern.popular {
                transform: none;
            }

            .pricing-card-modern.popular:hover {
                transform: translateY(-10px) scale(1.02);
            }

            .popular-ribbon {
                font-size: 0.7rem;
                padding: 0.4rem 2.5rem;
            }

            .price-number {
                font-size: 2.8rem;
            }

            .pricing-header-modern {
                padding: 2rem 1.5rem 1rem;
            }

            .pricing-features-modern {
                padding: 1.5rem;
            }

            .pricing-action-modern {
                padding: 0 1.5rem 2rem;
            }
        }

        .hero-buttons {
            position: relative;
            z-index: 2;
        }
        
        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            height: 100%;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0,123,255,0.1), transparent);
            transition: left 0.6s ease;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,123,255,0.2);
            border-color: var(--primary-color);
        }

        .feature-icon {
            font-size: 3.5rem;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotateY(360deg);
            color: var(--accent-color);
        }

        .feature-progress {
            position: relative;
            z-index: 2;
        }

        .feature-stat {
            padding: 1.5rem;
            border-radius: 15px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .feature-stat:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-icon {
            font-size: 2.5rem;
        }
        
        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 2rem;
            color: var(--text-dark);
            line-height: 1.3;
        }
        
        .pricing-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0,123,255,0.1), transparent);
            transition: left 0.6s ease;
        }

        .pricing-card:hover::before {
            left: 100%;
        }

        .pricing-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,123,255,0.2);
            border-color: var(--primary-color);
        }

        .pricing-card.popular {
            border-color: var(--accent-color);
            transform: scale(1.05);
            background: linear-gradient(135deg, #fff, #f8f9ff);
        }

        .pricing-card.popular::after {
            content: 'الأكثر شعبية';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, var(--accent-color), #20c997);
            color: white;
            padding: 0.6rem 2.5rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            z-index: 2;
        }
        
        .price {
            font-size: 3rem;
            font-weight: 800;
            color: var(--primary-color);
            margin: 1.5rem 0;
            text-align: center;
        }

        .price small {
            font-size: 1rem;
            color: #6c757d;
            font-weight: 400;
        }
        
        .footer {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 4rem 0 2rem;
            margin-top: 5rem;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }

        .footer-section h5,
        .footer-section h6 {
            color: white;
            font-weight: 700;
            margin-bottom: 1.5rem;
        }

        .footer-links {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 0.8rem;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .footer-links a:hover {
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .social-links {
            margin-top: 1.5rem;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-radius: 50%;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-3px);
        }

        .contact-info .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .contact-info .contact-item i {
            width: 20px;
            text-align: center;
            margin-left: 1rem;
            font-size: 1.1rem;
        }

        .contact-info a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-info a:hover {
            color: var(--primary-color);
        }

        .footer-legal a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .footer-legal a:hover {
            color: var(--primary-color);
        }

        /* Testimonials Styles */
        .testimonial-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .testimonial-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0,123,255,0.05), transparent);
            transition: left 0.6s ease;
        }

        .testimonial-card:hover::before {
            left: 100%;
        }

        .testimonial-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .testimonial-card.featured {
            border: 2px solid var(--primary-color);
            transform: scale(1.02);
        }

        .quote-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
        }

        .testimonial-text {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #495057;
            margin-bottom: 2rem;
            font-style: italic;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }

        .author-avatar {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--accent-color), #20c997);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-left: 1rem;
            flex-shrink: 0;
        }

        .author-name {
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.25rem;
        }

        .author-title {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }

        .rating {
            font-size: 0.9rem;
        }

        /* Enhanced Pricing Cards */
        .pricing-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .pricing-header h5 {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin: 1rem 0 0.5rem;
        }

        .pricing-features {
            margin: 2rem 0;
        }

        .pricing-features li {
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            font-size: 1rem;
            display: flex;
            align-items: center;
        }

        .pricing-features li:last-child {
            border-bottom: none;
        }

        .pricing-card .btn {
            border-radius: 50px;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .pricing-card .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        /* Trust Indicators */
        .trust-indicators {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 3rem;
            padding: 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .trust-item {
            text-align: center;
        }

        .trust-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .trust-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .trust-indicators {
                flex-direction: column;
                gap: 1.5rem;
            }

            .trust-number {
                font-size: 2rem;
            }
        }

        /* New Enhanced Pricing Cards */
        .pricing-card-new {
            background: white;
            border-radius: 25px;
            padding: 3rem 2rem;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0,0,0,0.08);
            transition: all 0.4s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .pricing-card-new::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0,123,255,0.05), transparent);
            transition: left 0.8s ease;
        }

        .pricing-card-new:hover::before {
            left: 100%;
        }

        .pricing-card-new:hover {
            transform: translateY(-20px) scale(1.03);
            box-shadow: 0 25px 50px rgba(0,123,255,0.15);
            border-color: var(--primary-color);
        }

        .pricing-card-new.popular {
            border-color: var(--accent-color);
            transform: scale(1.05);
            background: linear-gradient(135deg, #fff, #f8fff8);
        }

        .pricing-card-new.popular:hover {
            transform: translateY(-20px) scale(1.08);
            box-shadow: 0 30px 60px rgba(40, 167, 69, 0.2);
        }

        .popular-badge {
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, var(--accent-color), #20c997);
            color: white;
            padding: 0.6rem 2rem;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            z-index: 2;
        }

        .pricing-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 2rem;
            transition: all 0.4s ease;
        }

        .pricing-card-new.popular .pricing-icon {
            background: linear-gradient(135deg, var(--accent-color), #20c997);
        }

        .pricing-card-new:hover .pricing-icon {
            transform: scale(1.1) rotateY(360deg);
            box-shadow: 0 10px 25px rgba(0,123,255,0.3);
        }

        .pricing-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .pricing-subtitle {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 2rem;
        }

        .pricing-price {
            margin-bottom: 2.5rem;
            position: relative;
        }

        .pricing-price .currency {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
            vertical-align: top;
        }

        .pricing-price .amount {
            font-size: 3.5rem;
            font-weight: 800;
            color: var(--primary-color);
            margin: 0 0.2rem;
        }

        .pricing-price .period {
            font-size: 1rem;
            color: #6c757d;
            font-weight: 500;
        }

        .pricing-features-new {
            list-style: none;
            padding: 0;
            margin: 0 0 2.5rem 0;
        }

        .pricing-features-new li {
            padding: 0.8rem 0;
            font-size: 1rem;
            color: #495057;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        .pricing-features-new li:last-child {
            border-bottom: none;
        }

        .pricing-features-new li i {
            color: var(--accent-color);
            margin-left: 1rem;
            font-size: 1.1rem;
        }

        .pricing-btn {
            display: inline-block;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 1rem 2.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.4s ease;
            border: none;
            width: 100%;
            box-shadow: 0 8px 20px rgba(0,123,255,0.3);
        }

        .pricing-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(0,123,255,0.4);
            color: white;
        }

        .pricing-btn.featured {
            background: linear-gradient(135deg, var(--accent-color), #20c997);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }

        .pricing-btn.featured:hover {
            box-shadow: 0 12px 30px rgba(40, 167, 69, 0.4);
        }

        /* Additional Pricing Enhancements */
        .pricing-card-new::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .pricing-card-new:hover::after {
            opacity: 1;
        }

        .pricing-card-new.popular::after {
            opacity: 1;
            background: linear-gradient(90deg, var(--accent-color), #20c997);
        }

        /* Enhanced Pricing Cards - Better Design */
        .pricing-card-enhanced {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .pricing-card-enhanced:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,123,255,0.15);
            border-color: var(--primary-color);
        }

        .pricing-card-enhanced.popular {
            border-color: var(--accent-color);
            transform: scale(1.05);
        }

        .pricing-card-enhanced.popular:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 25px 50px rgba(40, 167, 69, 0.2);
        }

        .popular-badge-enhanced {
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, var(--accent-color), #20c997);
            color: white;
            padding: 0.6rem 1.5rem;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            z-index: 2;
        }

        .pricing-header-enhanced {
            text-align: center;
            padding: 2rem 2rem 1rem;
        }

        .pricing-icon-enhanced {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            margin: 0 auto 1.5rem;
            transition: all 0.3s ease;
        }

        .pricing-card-enhanced.popular .pricing-icon-enhanced {
            background: linear-gradient(135deg, var(--accent-color), #20c997);
        }

        .pricing-card-enhanced:hover .pricing-icon-enhanced {
            transform: scale(1.1);
        }

        .pricing-title-enhanced {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .pricing-subtitle-enhanced {
            font-size: 0.95rem;
            color: #6c757d;
            margin-bottom: 0;
        }

        .pricing-price-enhanced {
            text-align: center;
            padding: 1.5rem 2rem;
            background: rgba(0,123,255,0.05);
            margin: 0 1rem;
            border-radius: 15px;
        }

        .pricing-card-enhanced.popular .pricing-price-enhanced {
            background: rgba(40, 167, 69, 0.05);
        }

        .price-container {
            margin-bottom: 0.5rem;
        }

        .price-container .currency {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            vertical-align: top;
        }

        .price-container .amount {
            font-size: 2.8rem;
            font-weight: 800;
            color: var(--primary-color);
            margin: 0 0.2rem;
        }

        .price-container .period {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
        }

        .price-note {
            font-size: 0.8rem;
            color: var(--accent-color);
            font-weight: 600;
        }

        .pricing-features-enhanced {
            padding: 2rem;
        }

        .pricing-features-enhanced ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .pricing-features-enhanced li {
            display: flex;
            align-items: center;
            padding: 0.7rem 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        .pricing-features-enhanced li:last-child {
            border-bottom: none;
        }

        .pricing-features-enhanced li i {
            color: var(--accent-color);
            margin-left: 1rem;
            font-size: 1rem;
            width: 16px;
        }

        .pricing-features-enhanced li span {
            font-size: 0.95rem;
            color: #495057;
        }

        .pricing-footer-enhanced {
            padding: 0 2rem 2rem;
        }

        .pricing-btn-enhanced {
            display: block;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-align: center;
            box-shadow: 0 6px 20px rgba(0,123,255,0.3);
        }

        .pricing-btn-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,123,255,0.4);
            color: white;
        }

        .pricing-btn-enhanced.featured {
            background: linear-gradient(135deg, var(--accent-color), #20c997);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
        }

        .pricing-btn-enhanced.featured:hover {
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        /* Guarantee Badge */
        .guarantee-badge {
            display: inline-flex;
            align-items: center;
            background: rgba(40, 167, 69, 0.1);
            color: var(--accent-color);
            padding: 0.7rem 1.5rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }
        
        .stats-section {
            background: var(--primary-color);
            color: white;
            padding: 3rem 0;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            display: block;
        }
        
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
            
            .section-title {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top" id="mainNavbar">
        <div class="container">
            <a class="navbar-brand animate__animated animate__fadeInLeft" href="index.php">
                <i class="fas fa-stethoscope me-2"></i>
                نظام حكيم
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="features.php">
                            <i class="fas fa-star"></i>الميزات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pricing.php">
                            <i class="fas fa-tags"></i>الأسعار
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">
                            <i class="fas fa-envelope"></i>تواصل معنا
                        </a>
                    </li>
                </ul>

                <div class="d-flex align-items-center">
                    <a href="login.php" class="btn btn-outline-primary">
                        <i class="fas fa-sign-in-alt"></i>دخول
                    </a>
                    <a href="register.php" class="btn btn-success">
                        <i class="fas fa-rocket"></i>ابدأ الآن
                    </a>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main style="margin-top: 85px;">
        <?php if (isset($_SESSION['notification'])): ?>
            <?php $notification = $_SESSION['notification']; unset($_SESSION['notification']); ?>
            <div class="alert alert-<?= $notification['type'] ?> alert-dismissible fade show" role="alert">
                <?= htmlspecialchars($notification['message']) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?= $content ?>
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row g-4">
                <!-- Company Info -->
                <div class="col-lg-4 col-md-6">
                    <div class="footer-section">
                        <h5 class="mb-4">
                            <i class="fas fa-stethoscope me-2 text-primary"></i>
                            نظام حكيم
                        </h5>
                        <p class="mb-4">نظام شامل ومتطور لإدارة العيادات الطبية بكفاءة عالية. نساعد الأطباء على تقديم أفضل رعاية طبية لمرضاهم.</p>

                        <div class="social-links">
                            <h6 class="mb-3">تابعنا على:</h6>
                            <div class="d-flex gap-3 flex-wrap">
                                <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="col-lg-2 col-md-6">
                    <div class="footer-section">
                        <h6 class="mb-4">روابط سريعة</h6>
                        <ul class="footer-links">
                            <li><a href="index.php">الرئيسية</a></li>
                            <li><a href="features.php">الميزات</a></li>
                            <li><a href="pricing.php">الأسعار</a></li>
                            <li><a href="#about">من نحن</a></li>
                            <li><a href="contact.php">تواصل معنا</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Services -->
                <div class="col-lg-2 col-md-6">
                    <div class="footer-section">
                        <h6 class="mb-4">الخدمات</h6>
                        <ul class="footer-links">
                            <li><a href="features.php">إدارة المواعيد</a></li>
                            <li><a href="features.php">إدارة المرضى</a></li>
                            <li><a href="features.php">التقارير</a></li>
                            <li><a href="features.php">الفوترة</a></li>
                            <li><a href="register.php">عرض تجريبي</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Support -->
                <div class="col-lg-2 col-md-6">
                    <div class="footer-section">
                        <h6 class="mb-4">الدعم</h6>
                        <ul class="footer-links">
                            <li><a href="contact.php">مركز المساعدة</a></li>
                            <li><a href="features.php">الوثائق</a></li>
                            <li><a href="features.php">دروس تعليمية</a></li>
                            <li><a href="contact.php">الأسئلة الشائعة</a></li>
                            <li><a href="contact.php">الدعم الفني</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Contact Info -->
                <div class="col-lg-2 col-md-6">
                    <div class="footer-section">
                        <h6 class="mb-4">تواصل معنا</h6>
                        <div class="contact-info">
                            <div class="contact-item mb-3">
                                <i class="fas fa-envelope me-2 text-primary"></i>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                            <div class="contact-item mb-3">
                                <i class="fas fa-phone me-2 text-primary"></i>
                                <a href="tel:+970594967220">+970594967220</a>
                            </div>
                            <div class="contact-item mb-3">
                                <i class="fab fa-whatsapp me-3 text-success"></i>
                                <a href="https://wa.me/970594967220">واتساب</a>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                <span>فلسطين، غزة</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Footer -->
            <hr class="my-4 opacity-25">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 نظام حكيم. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-legal">
                        <a href="#privacy" class="me-3">سياسة الخصوصية</a>
                        <a href="#terms" class="me-3">شروط الاستخدام</a>
                        <a href="#cookies">سياسة الكوكيز</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Enhanced Animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;

                    // Different animations for different elements
                    if (element.classList.contains('feature-card')) {
                        element.classList.add('animate__animated', 'animate__fadeInUp');
                        element.style.animationDelay = Math.random() * 0.5 + 's';
                    } else if (element.classList.contains('pricing-card')) {
                        element.classList.add('animate__animated', 'animate__zoomIn');
                        element.style.animationDelay = Math.random() * 0.3 + 's';
                    } else if (element.classList.contains('stat-item')) {
                        element.classList.add('animate__animated', 'animate__bounceIn');
                        element.style.animationDelay = Math.random() * 0.4 + 's';
                    } else {
                        element.classList.add('animate__animated', 'animate__fadeInUp');
                    }
                }
            });
        }, observerOptions);

        // Observe all animatable elements
        document.querySelectorAll('.feature-card, .pricing-card, .stat-item, .section-title').forEach(el => {
            observer.observe(el);
        });

        // Hero section typing effect
        document.addEventListener('DOMContentLoaded', function() {
            const heroTitle = document.querySelector('.hero-title');
            if (heroTitle) {
                heroTitle.classList.add('animate__animated', 'animate__fadeInDown');
            }

            const heroSubtitle = document.querySelector('.hero-subtitle');
            if (heroSubtitle) {
                heroSubtitle.style.animationDelay = '0.3s';
                heroSubtitle.classList.add('animate__animated', 'animate__fadeInUp');
            }

            const heroButtons = document.querySelector('.hero-buttons');
            if (heroButtons) {
                heroButtons.style.animationDelay = '0.6s';
                heroButtons.classList.add('animate__animated', 'animate__fadeInUp');
            }
        });

        // Counter animation for stats
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 100;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current).toLocaleString();
            }, 20);
        }

        // Animate counters when they come into view
        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target.querySelector('.stat-number');
                    if (counter && !counter.classList.contains('animated')) {
                        counter.classList.add('animated');
                        const target = parseInt(counter.getAttribute('data-target'));
                        animateCounter(counter, target);
                    }
                }
            });
        });

        document.querySelectorAll('.stat-item').forEach(el => {
            statsObserver.observe(el);
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Progress bars animation
        function animateProgressBars() {
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const width = bar.getAttribute('data-width');
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        }

        // Trigger progress bars when features section is visible
        const featuresSection = document.getElementById('features-section');
        if (featuresSection) {
            const featuresObserver = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateProgressBars();
                        featuresObserver.unobserve(entry.target);
                    }
                });
            });
            featuresObserver.observe(featuresSection);
        }

        // Parallax effect for hero section
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const heroSection = document.querySelector('.hero-section');
            if (heroSection) {
                heroSection.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });

        // Smooth reveal animation for elements
        function revealOnScroll() {
            const reveals = document.querySelectorAll('.reveal');
            reveals.forEach(element => {
                const windowHeight = window.innerHeight;
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;

                if (elementTop < windowHeight - elementVisible) {
                    element.classList.add('active');
                }
            });
        }

        window.addEventListener('scroll', revealOnScroll);

        // Add scroll effect to navbar
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('mainNavbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>

    <!-- Enhanced Button Styles -->
    <style>
        /* Enhanced CTA Button */
        .enhanced-cta-btn {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            border: none !important;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            transition: all 0.4s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-radius: 50px !important;
            padding: 15px 35px !important;
        }

        .enhanced-cta-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
            background: linear-gradient(135deg, #20c997, #28a745) !important;
        }

        .enhanced-cta-btn:active {
            transform: translateY(-1px);
        }

        /* Enhanced Light CTA Button */
        .enhanced-cta-btn-light {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #ffffff, #f8f9fa) !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            color: #007bff !important;
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
            transition: all 0.4s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-radius: 50px !important;
            padding: 15px 35px !important;
        }

        .enhanced-cta-btn-light:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 255, 255, 0.3);
            background: linear-gradient(135deg, #f8f9fa, #ffffff) !important;
            color: #0056b3 !important;
        }

        .enhanced-cta-btn-light:active {
            transform: translateY(-1px);
        }

        /* Button Shine Effect */
        .btn-shine {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .enhanced-cta-btn:hover .btn-shine,
        .enhanced-cta-btn-light:hover .btn-shine {
            left: 100%;
        }

        /* Enhanced Social Links */
        .social-links .d-flex {
            justify-content: flex-start;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 45px !important;
            height: 45px !important;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-radius: 50%;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            border: 2px solid transparent;
        }

        .social-link:hover {
            background: var(--primary-color) !important;
            color: white !important;
            transform: translateY(-3px) scale(1.1);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 20px rgba(0, 123, 255, 0.3);
        }

        /* Specific social media colors on hover */
        .social-link:nth-child(1):hover { background: #1877f2 !important; } /* Facebook */
        .social-link:nth-child(2):hover { background: #1da1f2 !important; } /* Twitter */
        .social-link:nth-child(3):hover { background: #0077b5 !important; } /* LinkedIn */
        .social-link:nth-child(4):hover { background: #e4405f !important; } /* Instagram */
        .social-link:nth-child(5):hover { background: #ff0000 !important; } /* YouTube */

        /* Enhanced Login Button */
        .enhanced-login-btn {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #007bff, #0056b3) !important;
            border: none !important;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
            transition: all 0.4s ease;
            font-weight: 600;
            border-radius: 10px !important;
            padding: 15px 30px !important;
        }

        .enhanced-login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0, 123, 255, 0.4);
            background: linear-gradient(135deg, #0056b3, #007bff) !important;
        }

        /* Enhanced Register Button */
        .enhanced-register-btn {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            border: none !important;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            transition: all 0.4s ease;
            font-weight: 600;
            border-radius: 10px !important;
            padding: 15px 30px !important;
        }

        .enhanced-register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
            background: linear-gradient(135deg, #20c997, #28a745) !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .enhanced-cta-btn,
            .enhanced-cta-btn-light,
            .enhanced-login-btn,
            .enhanced-register-btn {
                padding: 12px 25px !important;
                font-size: 0.9rem;
            }

            .social-links .d-flex {
                justify-content: center;
            }
        }
    </style>
</body>
</html>
