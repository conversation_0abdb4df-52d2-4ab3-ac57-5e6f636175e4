<?php
/**
 * صفحة الوصفات الطبية
 */

session_start();

define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('VIEWS_PATH', APP_PATH . '/views');

require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/models/Prescription.php';
require_once APP_PATH . '/models/Patient.php';
require_once APP_PATH . '/controllers/PrescriptionsController.php';

require_once APP_PATH . '/core/AuthGuard.php';
requireAuth();

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$controller = new PrescriptionsController();
$action = $_GET['action'] ?? 'index';
$id = $_GET['id'] ?? null;

switch ($action) {
    case 'index':
        $controller->index();
        break;
    case 'add':
        $controller->add();
        break;
    case 'edit':
        if ($id) {
            $controller->edit($id);
        } else {
            header('Location: prescriptions.php');
        }
        break;
    case 'view':
        if ($id) {
            $controller->viewPrescription($id);
        } else {
            header('Location: prescriptions.php');
        }
        break;
    case 'delete':
        if ($id) {
            $controller->delete($id);
        } else {
            header('Location: prescriptions.php');
        }
        break;
    case 'print':
        if ($id) {
            $controller->printPrescription($id);
        } else {
            header('Location: prescriptions.php');
        }
        break;
    case 'complete':
        if ($id) {
            $controller->complete($id);
        } else {
            header('Location: prescriptions.php');
        }
        break;
    default:
        $controller->index();
        break;
} 