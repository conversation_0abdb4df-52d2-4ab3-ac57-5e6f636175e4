<?php
/**
 * ملف الإعدادات العامة للتطبيق
 * General application configuration file
 */

// تعريف المسارات الأساسية (فقط إذا لم تكن معرفة مسبقاً)
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', __DIR__ . '/..');
}
if (!defined('APP_PATH')) {
    define('APP_PATH', ROOT_PATH . '/app');
}
if (!defined('CONFIG_PATH')) {
    define('CONFIG_PATH', ROOT_PATH . '/config');
}
if (!defined('PUBLIC_PATH')) {
    define('PUBLIC_PATH', ROOT_PATH . '/public');
}
if (!defined('VIEWS_PATH')) {
    define('VIEWS_PATH', APP_PATH . '/views');
}

// إعدادات التطبيق الأساسية
if (!defined('APP_NAME')) {
    define('APP_NAME', 'حكيم - Hakim');
}
if (!defined('APP_VERSION')) {
    define('APP_VERSION', '1.0.0');
}
if (!defined('APP_URL')) {
    define('APP_URL', 'https://bujairi.shop');
}
if (!defined('APP_TIMEZONE')) {
    define('APP_TIMEZONE', 'Asia/Jerusalem');
}

// إعدادات قاعدة البيانات
if (!defined('DB_HOST')) {
    define('DB_HOST', 'localhost');
}
if (!defined('DB_NAME')) {
    define('DB_NAME', 'bujairi_altarda');
}
if (!defined('DB_USER')) {
    define('DB_USER', 'bujairi_altarda');
}
if (!defined('DB_PASS')) {
    define('DB_PASS', 'bujairi_altarda');
}
if (!defined('DB_CHARSET')) {
    define('DB_CHARSET', 'utf8mb4');
}

// إعدادات الأمان
if (!defined('ENCRYPTION_KEY')) {
    define('ENCRYPTION_KEY', 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6');
}
if (!defined('SESSION_LIFETIME')) {
    define('SESSION_LIFETIME', 3600);
}

// إعدادات الملفات
if (!defined('UPLOAD_PATH')) {
    define('UPLOAD_PATH', PUBLIC_PATH . '/uploads');
}
if (!defined('MAX_FILE_SIZE')) {
    define('MAX_FILE_SIZE', 5 * 1024 * 1024);
}
if (!defined('ALLOWED_EXTENSIONS')) {
    define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']);
}

// إعدادات البريد الإلكتروني (للإشعارات)
if (!defined('MAIL_HOST')) {
    define('MAIL_HOST', 'smtp.gmail.com');
}
if (!defined('MAIL_PORT')) {
    define('MAIL_PORT', 587);
}
if (!defined('MAIL_USERNAME')) {
    define('MAIL_USERNAME', '');
}
if (!defined('MAIL_PASSWORD')) {
    define('MAIL_PASSWORD', '');
}
if (!defined('MAIL_FROM_EMAIL')) {
    define('MAIL_FROM_EMAIL', '<EMAIL>');
}
if (!defined('MAIL_FROM_NAME')) {
    define('MAIL_FROM_NAME', 'نظام حكيم');
}

// إعدادات الاشتراكات (بالشيكل الإسرائيلي)
if (!defined('TRIAL_DAYS')) {
    define('TRIAL_DAYS', 15);
}
if (!defined('BASIC_PLAN_PRICE')) {
    define('BASIC_PLAN_PRICE', 149);
}
if (!defined('PRO_PLAN_PRICE')) {
    define('PRO_PLAN_PRICE', 299);
}
if (!defined('ENTERPRISE_PLAN_PRICE')) {
    define('ENTERPRISE_PLAN_PRICE', 599);
}
if (!defined('CURRENCY')) {
    define('CURRENCY', 'ILS');
}
if (!defined('CURRENCY_SYMBOL')) {
    define('CURRENCY_SYMBOL', '₪');
}

// تعيين المنطقة الزمنية
date_default_timezone_set(APP_TIMEZONE);

// تشغيل عرض الأخطاء في بيئة التطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
