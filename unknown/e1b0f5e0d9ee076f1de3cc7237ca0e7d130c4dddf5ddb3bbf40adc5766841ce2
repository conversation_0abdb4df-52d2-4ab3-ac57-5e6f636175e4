<?php
/**
 * موديل الفواتير
 * Invoice Model
 */

class Invoice extends Model {
    protected $table = 'invoices';
    
    /**
     * الحصول على الفواتير حسب العيادة
     * Get invoices by clinic
     */
    public function getInvoicesByClinic($clinicId, $dateFrom = null, $dateTo = null, $status = 'all', $search = '') {
        $sql = "SELECT i.*, p.full_name as patient_name, p.phone as patient_phone, p.email as patient_email
                FROM {$this->table} i
                LEFT JOIN patients p ON i.patient_id = p.id
                WHERE i.clinic_id = ?";
        
        $params = [$clinicId];
        
        // تطبيق الفلاتر
        if ($dateFrom && $dateTo) {
            $sql .= " AND i.invoice_date BETWEEN ? AND ?";
            $params[] = $dateFrom;
            $params[] = $dateTo;
        }
        
        if ($status !== 'all') {
            $sql .= " AND i.status = ?";
            $params[] = $status;
        }
        
        if ($search) {
            $sql .= " AND (p.full_name LIKE ? OR i.invoice_number LIKE ?)";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }
        
        $sql .= " ORDER BY i.invoice_date DESC, i.created_at DESC";
        
        return $this->db->select($sql, $params);
    }
    
    /**
     * الحصول على تفاصيل الفاتورة
     * Get invoice details
     */
    public function getInvoiceDetails($id) {
        $sql = "SELECT i.*, p.full_name as patient_name, p.phone as patient_phone, 
                       p.email as patient_email, p.date_of_birth, p.gender,
                       u.full_name as doctor_name
                FROM {$this->table} i
                LEFT JOIN patients p ON i.patient_id = p.id
                LEFT JOIN users u ON i.created_by = u.id
                WHERE i.id = ?";
        
        return $this->db->selectOne($sql, [$id]);
    }
    
    /**
     * الحصول على إحصائيات الفواتير
     * Get invoice statistics
     */
    public function getStats($clinicId) {
        $today = date('Y-m-d');
        $thisMonth = date('Y-m');
        
        $stats = [];
        
        // فواتير اليوم
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE clinic_id = ? AND DATE(invoice_date) = ?";
        $result = $this->db->selectOne($sql, [$clinicId, $today]);
        $stats['today'] = $result ? $result['count'] : 0;
        
        // فواتير مدفوعة
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE clinic_id = ? AND status = 'paid'";
        $result = $this->db->selectOne($sql, [$clinicId]);
        $stats['paid'] = $result ? $result['count'] : 0;
        
        // فواتير معلقة
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE clinic_id = ? AND status = 'pending'";
        $result = $this->db->selectOne($sql, [$clinicId]);
        $stats['pending'] = $result ? $result['count'] : 0;
        
        // إجمالي المبالغ لهذا الشهر
        $sql = "SELECT SUM(total_amount) as total FROM {$this->table} WHERE clinic_id = ? AND DATE_FORMAT(invoice_date, '%Y-%m') = ?";
        $result = $this->db->selectOne($sql, [$clinicId, $thisMonth]);
        $stats['total_amount'] = $result ? ($result['total'] ?? 0) : 0;
        
        return $stats;
    }
    
    /**
     * الحصول على آخر رقم فاتورة
     * Get last invoice number
     */
    public function getLastInvoiceNumber($year, $month) {
        $sql = "SELECT invoice_number FROM {$this->table} 
                WHERE invoice_number LIKE ? 
                ORDER BY invoice_number DESC 
                LIMIT 1";
        
        $pattern = "INV-{$year}{$month}-%";
        $result = $this->db->selectOne($sql, [$pattern]);
        
        return $result ? $result['invoice_number'] : null;
    }
    
    /**
     * إنشاء فاتورة جديدة
     * Create new invoice
     */
    public function create($data) {
        $fields = ['clinic_id', 'patient_id', 'invoice_number', 'invoice_date', 'services', 
                   'subtotal', 'discount', 'total_amount', 'status', 'notes', 'created_by', 'created_at'];
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        
        $sql = "INSERT INTO {$this->table} (" . implode(',', $fields) . ") VALUES ($placeholders)";
        
        $values = [];
        foreach ($fields as $field) {
            $values[] = $data[$field] ?? null;
        }
        
        return $this->db->query($sql, $values);
    }
    
    /**
     * تحديث الفاتورة
     * Update invoice
     */
    public function update($id, $data) {
        $fields = ['patient_id', 'invoice_date', 'services', 'subtotal', 'discount', 
                   'total_amount', 'status', 'notes', 'updated_at'];
        $setClause = implode(' = ?, ', $fields) . ' = ?';
        
        $sql = "UPDATE {$this->table} SET $setClause WHERE id = ?";
        
        $values = [];
        foreach ($fields as $field) {
            $values[] = $data[$field] ?? null;
        }
        $values[] = $id;
        
        return $this->db->query($sql, $values);
    }
    
    /**
     * حذف الفاتورة
     * Delete invoice
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE id = ?";
        return $this->db->query($sql, [$id]);
    }
    
    /**
     * البحث عن فاتورة
     * Find invoice
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE id = ?";
        return $this->db->selectOne($sql, [$id]);
    }
} 