<?php
/**
 * كونترولر الإعدادات
 * Settings Controller
 */

class SettingsController extends Controller {

    public function __construct() {
        parent::__construct();
        // تحميل الموديلات المطلوبة
        require_once APP_PATH . '/models/User.php';
        require_once APP_PATH . '/models/Clinic.php';
    }
    
    /**
     * الصفحة الرئيسية للإعدادات
     * Main settings page
     */
    public function index() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $clinicModel = $this->model('Clinic');
        $userModel = $this->model('User');
        
        $clinic = $clinicModel->find($_SESSION['clinic_id']);
        $user = $userModel->find($_SESSION['user_id']);
        
        $data = [
            'title' => 'إعدادات العيادة',
            'clinic' => $clinic,
            'user' => $user
        ];
        
        $this->view->renderWithLayout('settings/index', 'dashboard', $data);
    }
    
    /**
     * إعدادات العيادة
     * Clinic settings
     */
    public function clinic() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleClinicSettings();
        }
        
        $clinicModel = $this->model('Clinic');
        $clinic = $clinicModel->find($_SESSION['clinic_id']);
        
        $data = [
            'title' => 'إعدادات العيادة',
            'clinic' => $clinic
        ];
        
        $this->view->renderWithLayout('settings/clinic', 'dashboard', $data);
    }
    
    /**
     * إعدادات المستخدم
     * User settings
     */
    public function profile() {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleProfileSettings();
        }
        
        $userModel = $this->model('User');
        $user = $userModel->find($_SESSION['user_id']);
        
        $data = [
            'title' => 'الملف الشخصي',
            'user' => $user
        ];
        
        $this->view->renderWithLayout('settings/profile', 'dashboard', $data);
    }
    
    /**
     * تغيير كلمة المرور
     * Change password
     */
    public function password() {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handlePasswordChange();
        }
        
        $data = [
            'title' => 'تغيير كلمة المرور'
        ];
        
        $this->view->renderWithLayout('settings/password', 'dashboard', $data);
    }
    
    /**
     * إعدادات المواعيد
     * Appointments settings
     */
    public function appointments() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleAppointmentSettings();
        }
        
        $settings = $this->getAppointmentSettings();
        
        $data = [
            'title' => 'إعدادات المواعيد',
            'settings' => $settings
        ];
        
        $this->view->renderWithLayout('settings/appointments', 'dashboard', $data);
    }
    
    /**
     * إعدادات الإشعارات
     * Notifications settings
     */
    public function notifications() {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleNotificationSettings();
        }
        
        $settings = $this->getNotificationSettings();
        
        $data = [
            'title' => 'إعدادات الإشعارات',
            'settings' => $settings
        ];
        
        $this->view->renderWithLayout('settings/notifications', 'dashboard', $data);
    }
    
    /**
     * النسخ الاحتياطي
     * Backup settings
     */
    public function backup() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $action = $_GET['backup_action'] ?? '';
        
        if ($action === 'create') {
            $this->createBackup();
        } elseif ($action === 'restore' && isset($_GET['file'])) {
            $this->restoreBackup($_GET['file']);
        }
        
        $backups = $this->getBackupList();
        
        $data = [
            'title' => 'النسخ الاحتياطي',
            'backups' => $backups
        ];
        
        $this->view->renderWithLayout('settings/backup', 'dashboard', $data);
    }
    
    /**
     * معالجة إعدادات العيادة
     * Handle clinic settings
     */
    private function handleClinicSettings() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'name' => 'required|min:3|max:100',
            'phone' => 'required|phone',
            'email' => 'email',
            'address' => 'max:255',
            'specialization' => 'required|max:100',
            'working_hours_start' => 'required',
            'working_hours_end' => 'required',
            'appointment_duration' => 'required|numeric|min:15|max:120'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        $clinicModel = $this->model('Clinic');
        
        $clinicData = [
            'name' => $data['name'],
            'phone' => $data['phone'],
            'email' => $data['email'] ?? null,
            'address' => $data['address'] ?? null,
            'specialization' => $data['specialization'],
            'working_hours_start' => $data['working_hours_start'],
            'working_hours_end' => $data['working_hours_end'],
            'appointment_duration' => $data['appointment_duration'],
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($clinicModel->update($_SESSION['clinic_id'], $clinicData)) {
            $this->notify('تم تحديث إعدادات العيادة بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء تحديث الإعدادات', 'error');
        }
        
        $this->redirect('settings/clinic');
    }
    
    /**
     * معالجة إعدادات الملف الشخصي
     * Handle profile settings
     */
    private function handleProfileSettings() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'name' => 'required|min:3|max:100',
            'email' => 'required|email',
            'phone' => 'phone'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        $userModel = $this->model('User');
        
        // التحقق من عدم تكرار البريد الإلكتروني
        if ($userModel->emailExists($data['email'], $_SESSION['user_id'])) {
            $this->notify('البريد الإلكتروني مستخدم مسبقاً', 'error');
            return;
        }
        
        $userData = [
            'full_name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($userModel->update($_SESSION['user_id'], $userData)) {
            $_SESSION['user_name'] = $data['name'];
            $_SESSION['user_email'] = $data['email'];
            $this->notify('تم تحديث الملف الشخصي بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء تحديث الملف الشخصي', 'error');
        }
        
        $this->redirect('settings/profile');
    }
    
    /**
     * معالجة تغيير كلمة المرور
     * Handle password change
     */
    private function handlePasswordChange() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'current_password' => 'required',
            'new_password' => 'required|min:6',
            'confirm_password' => 'required'
        ]);
        
        if (!empty($errors)) {
            $this->notify('يرجى تصحيح الأخطاء التالية: ' . implode(', ', $errors), 'error');
            return;
        }
        
        if ($data['new_password'] !== $data['confirm_password']) {
            $this->notify('كلمة المرور الجديدة وتأكيدها غير متطابقين', 'error');
            return;
        }
        
        $userModel = $this->model('User');
        $user = $userModel->find($_SESSION['user_id']);
        
        if (!password_verify($data['current_password'], $user['password'])) {
            $this->notify('كلمة المرور الحالية غير صحيحة', 'error');
            return;
        }
        
        $userData = [
            'password' => password_hash($data['new_password'], PASSWORD_DEFAULT),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($userModel->update($_SESSION['user_id'], $userData)) {
            $this->notify('تم تغيير كلمة المرور بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء تغيير كلمة المرور', 'error');
        }
        
        $this->redirect('settings/password');
    }
    
    /**
     * معالجة إعدادات المواعيد
     * Handle appointment settings
     */
    private function handleAppointmentSettings() {
        $data = $this->sanitize($_POST);
        
        // حفظ إعدادات المواعيد في جدول الإعدادات
        $this->saveSettings('appointments', $data);
        
        $this->notify('تم تحديث إعدادات المواعيد بنجاح', 'success');
        $this->redirect('settings/appointments');
    }
    
    /**
     * معالجة إعدادات الإشعارات
     * Handle notification settings
     */
    private function handleNotificationSettings() {
        $data = $this->sanitize($_POST);
        
        // حفظ إعدادات الإشعارات في جدول الإعدادات
        $this->saveSettings('notifications', $data);
        
        $this->notify('تم تحديث إعدادات الإشعارات بنجاح', 'success');
        $this->redirect('settings/notifications');
    }
    
    /**
     * الحصول على إعدادات المواعيد
     * Get appointment settings
     */
    private function getAppointmentSettings() {
        return $this->getSettings('appointments', [
            'auto_confirm' => false,
            'reminder_hours' => 24,
            'allow_online_booking' => true,
            'max_advance_days' => 30
        ]);
    }
    
    /**
     * الحصول على إعدادات الإشعارات
     * Get notification settings
     */
    private function getNotificationSettings() {
        return $this->getSettings('notifications', [
            'email_notifications' => true,
            'sms_notifications' => false,
            'appointment_reminders' => true,
            'new_patient_alerts' => true
        ]);
    }
    
    /**
     * حفظ الإعدادات
     * Save settings
     */
    private function saveSettings($category, $data) {
        // هذا مثال - يحتاج لجدول الإعدادات
        // يمكن حفظ الإعدادات في ملف JSON أو جدول منفصل
    }
    
    /**
     * الحصول على الإعدادات
     * Get settings
     */
    private function getSettings($category, $defaults = []) {
        // هذا مثال - يحتاج لجدول الإعدادات
        return $defaults;
    }
    
    /**
     * إنشاء نسخة احتياطية
     * Create backup
     */
    private function createBackup() {
        // تنفيذ إنشاء النسخة الاحتياطية
        $this->notify('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        $this->redirect('settings/backup');
    }
    
    /**
     * استعادة النسخة الاحتياطية
     * Restore backup
     */
    private function restoreBackup($file) {
        // تنفيذ استعادة النسخة الاحتياطية
        $this->notify('تم استعادة النسخة الاحتياطية بنجاح', 'success');
        $this->redirect('settings/backup');
    }
    
    /**
     * الحصول على قائمة النسخ الاحتياطية
     * Get backup list
     */
    private function getBackupList() {
        // إرجاع قائمة النسخ الاحتياطية المتاحة
        return [];
    }
}
?>
