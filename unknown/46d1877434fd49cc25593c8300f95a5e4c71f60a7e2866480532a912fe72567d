<?php
/**
 * الكلاس الأساسي للكونترولرز
 * Base Controller class
 */

class Controller {
    protected $db;
    protected $view;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->view = new View();
    }
    
    /**
     * تحميل موديل
     * Load a model
     */
    protected function model($model) {
        $modelFile = APP_PATH . '/models/' . $model . '.php';
        if (file_exists($modelFile)) {
            require_once $modelFile;
            return new $model();
        }
        return null;
    }
    
    /**
     * عرض صفحة
     * Render a view
     */
    protected function view($view, $data = []) {
        $this->view->render($view, $data);
    }
    
    /**
     * إعادة توجيه
     * Redirect to URL
     */
    protected function redirect($url) {
        header('Location: ' . $this->url($url));
        exit;
    }

    /**
     * إنشاء رابط
     * Generate URL
     */
    protected function url($path = '') {
        $baseUrl = APP_URL ?? '';
        if (empty($path)) {
            return $baseUrl;
        }
        return $baseUrl . '/' . ltrim($path, '/');
    }
    
    /**
     * إرجاع JSON
     * Return JSON response
     */
    protected function json($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * التحقق من تسجيل الدخول
     * Check if user is logged in
     */
    protected function requireAuth() {
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('login');
        }
    }
    
    /**
     * التحقق من الصلاحيات
     * Check user permissions
     */
    protected function requireRole($role) {
        $this->requireAuth();
        if ($_SESSION['user_role'] !== $role && $_SESSION['user_role'] !== 'admin') {
            $this->view('errors/403');
            exit;
        }
    }
    
    /**
     * تنظيف البيانات المدخلة
     * Sanitize input data
     */
    protected function sanitize($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitize'], $data);
        }
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * التحقق من صحة البيانات
     * Validate input data
     */
    protected function validate($data, $rules) {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? '';
            
            if (strpos($rule, 'required') !== false && empty($value)) {
                $errors[$field] = "هذا الحقل مطلوب";
                continue;
            }
            
            if (strpos($rule, 'email') !== false && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                $errors[$field] = "البريد الإلكتروني غير صحيح";
            }
            
            if (strpos($rule, 'phone') !== false && !preg_match('/^[0-9+\-\s]+$/', $value)) {
                $errors[$field] = "رقم الهاتف غير صحيح";
            }
            
            if (preg_match('/min:(\d+)/', $rule, $matches) && strlen($value) < $matches[1]) {
                $errors[$field] = "يجب أن يكون الحد الأدنى {$matches[1]} أحرف";
            }
            
            if (preg_match('/max:(\d+)/', $rule, $matches) && strlen($value) > $matches[1]) {
                $errors[$field] = "يجب أن يكون الحد الأقصى {$matches[1]} أحرف";
            }
        }
        
        return $errors;
    }
    
    /**
     * رفع ملف
     * Upload file
     */
    protected function uploadFile($file, $folder = 'general') {
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            return false;
        }
        
        $uploadDir = UPLOAD_PATH . '/' . $folder;
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, ALLOWED_EXTENSIONS)) {
            return false;
        }
        
        if ($file['size'] > MAX_FILE_SIZE) {
            return false;
        }
        
        $filename = uniqid() . '.' . $extension;
        $filepath = $uploadDir . '/' . $filename;
        
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            return $folder . '/' . $filename;
        }
        
        return false;
    }
    
    /**
     * إرسال إشعار
     * Send notification
     */
    protected function notify($message, $type = 'success') {
        $_SESSION['notification'] = [
            'message' => $message,
            'type' => $type
        ];
    }
    
    /**
     * الحصول على الإشعارات
     * Get notifications
     */
    protected function getNotifications() {
        if (isset($_SESSION['notification'])) {
            $notification = $_SESSION['notification'];
            unset($_SESSION['notification']);
            return $notification;
        }
        return null;
    }
}
?>
