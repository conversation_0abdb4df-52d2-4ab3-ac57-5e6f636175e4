<?php
/**
 * الكلاس الأساسي للموديلز
 * Base Model class
 */

class Model {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع السجلات
     * Get all records
     */
    public function all($orderBy = null) {
        $sql = "SELECT * FROM {$this->table}";
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        return $this->db->fetchAll($sql);
    }

    /**
     * البحث عن سجل بالمعرف
     * Find record by ID
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ?";
        return $this->db->fetch($sql, [$id]);
    }

    /**
     * البحث عن سجل بشرط
     * Find record by condition
     */
    public function findBy($column, $value) {
        $sql = "SELECT * FROM {$this->table} WHERE {$column} = ?";
        return $this->db->fetch($sql, [$value]);
    }

    /**
     * البحث عن سجلات بشرط
     * Find records by condition
     */
    public function where($column, $value, $operator = '=') {
        $sql = "SELECT * FROM {$this->table} WHERE {$column} {$operator} ?";
        return $this->db->fetchAll($sql, [$value]);
    }
    
    /**
     * إنشاء سجل جديد
     * Create new record
     */
    public function create($data) {
        // إضافة التاريخ والوقت
        if (!isset($data['created_at'])) {
            $data['created_at'] = date('Y-m-d H:i:s');
        }
        if (!isset($data['updated_at'])) {
            $data['updated_at'] = date('Y-m-d H:i:s');
        }
        
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * تحديث سجل
     * Update record
     */
    public function update($id, $data) {
        $data['updated_at'] = date('Y-m-d H:i:s');
        return $this->db->update($this->table, $data, "{$this->primaryKey} = :id", ['id' => $id]);
    }
    
    /**
     * حذف سجل
     * Delete record
     */
    public function delete($id) {
        return $this->db->delete($this->table, "{$this->primaryKey} = :id", ['id' => $id]);
    }

    /**
     * عد السجلات
     * Count records
     */
    public function count($where = null, $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        if ($where) {
            $sql .= " WHERE {$where}";
        }
        $result = $this->db->fetch($sql, $params);
        return $result ? $result['count'] : 0;
    }
    
    /**
     * البحث في النص
     * Search in text
     */
    public function search($columns, $term) {
        $conditions = [];
        $params = [];

        foreach ($columns as $column) {
            $conditions[] = "{$column} LIKE ?";
            $params[] = "%{$term}%";
        }

        $sql = "SELECT * FROM {$this->table} WHERE " . implode(' OR ', $conditions);
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * الحصول على سجلات مع التصفح
     * Get paginated records
     */
    public function paginate($page = 1, $perPage = 10, $where = null, $params = []) {
        $offset = ($page - 1) * $perPage;
        
        $sql = "SELECT * FROM {$this->table}";
        if ($where) {
            $sql .= " WHERE {$where}";
        }
        $sql .= " LIMIT {$perPage} OFFSET {$offset}";
        
        $records = $this->db->select($sql, $params);
        $total = $this->count($where, $params);
        
        return [
            'data' => $records,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * تنفيذ استعلام مخصص
     * Execute custom query
     */
    public function query($sql, $params = []) {
        return $this->db->select($sql, $params);
    }
    
    /**
     * تنفيذ استعلام مخصص لسجل واحد
     * Execute custom query for single record
     */
    public function queryOne($sql, $params = []) {
        return $this->db->selectOne($sql, $params);
    }
    
    /**
     * بدء معاملة
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->db->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     * Commit transaction
     */
    public function commit() {
        return $this->db->commit();
    }
    
    /**
     * إلغاء المعاملة
     * Rollback transaction
     */
    public function rollback() {
        return $this->db->rollback();
    }
}
?>
