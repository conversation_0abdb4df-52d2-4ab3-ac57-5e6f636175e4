<?php
/**
 * AuthGuard - حماية الدخول والخروج
 * دوال مساعدة لإدارة حماية الصفحات
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * منع دخول المسجلين للصفحات العامة
 * إذا كان المستخدم مسجل الدخول، يوجهه للوحة التحكم
 */
function redirectIfAuthenticated() {
    if (isset($_SESSION['user_id'])) {
        header('Location: dashboard.php');
        exit;
    }
}

/**
 * منع غير المسجلين من دخول الصفحات المحمية
 * إذا لم يكن المستخدم مسجل الدخول، يوجهه لتسجيل الدخول
 */
function requireAuth() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit;
    }
}

/**
 * منع غير الأدمن من دخول صفحات الأدمن
 * إذا لم يكن المستخدم أدمن، يوجهه لتسجيل الدخول
 */
function adminOnly() {
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
        header('Location: login.php');
        exit;
    }
} 