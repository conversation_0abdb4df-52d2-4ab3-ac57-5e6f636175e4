<?php
/**
 * صفحة إعدادات الإشعارات
 * Notifications Settings Page
 */
?>

<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-bell me-2 text-warning"></i>
                إعدادات الإشعارات
            </h1>
            <p class="text-muted">تخصيص الإشعارات والتنبيهات حسب احتياجك</p>
        </div>
        <div>
            <a href="settings.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للإعدادات
            </a>
        </div>
    </div>

    <!-- رسائل الإشعارات -->
    <?php $this->showNotifications(); ?>

    <form method="POST" action="settings.php?action=notifications">
        <?= $this->csrfField() ?>
        <div class="row">
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-envelope me-2 text-primary"></i>
                            إشعارات البريد الإلكتروني
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" name="email_notifications" id="email_notifications" value="1" <?= ($settings['email_notifications'] ?? true) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="email_notifications">
                                تفعيل إشعارات البريد الإلكتروني
                            </label>
                        </div>
                        <div class="mb-3">
                            <label for="notification_email" class="form-label">البريد الإلكتروني المرسل منه الإشعارات</label>
                            <input type="email" name="notification_email" id="notification_email" class="form-control" value="<?= $settings['notification_email'] ?? '' ?>">
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sms me-2 text-success"></i>
                            إشعارات الرسائل القصيرة (SMS)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" name="sms_notifications" id="sms_notifications" value="1" <?= ($settings['sms_notifications'] ?? false) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="sms_notifications">
                                تفعيل إشعارات الرسائل القصيرة
                            </label>
                        </div>
                        <div class="mb-3">
                            <label for="sms_sender" class="form-label">اسم المرسل في الرسائل القصيرة</label>
                            <input type="text" name="sms_sender" id="sms_sender" class="form-control" value="<?= $settings['sms_sender'] ?? '' ?>">
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-check me-2 text-info"></i>
                            تذكيرات المواعيد
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" name="appointment_reminders" id="appointment_reminders" value="1" <?= ($settings['appointment_reminders'] ?? true) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="appointment_reminders">
                                تفعيل تذكيرات المواعيد
                            </label>
                        </div>
                        <div class="mb-3">
                            <label for="reminder_time" class="form-label">وقت التذكير قبل الموعد (ساعات)</label>
                            <select name="reminder_time" id="reminder_time" class="form-select">
                                <option value="2" <?= ($settings['reminder_time'] ?? 24) == 2 ? 'selected' : '' ?>>2 ساعة</option>
                                <option value="6" <?= ($settings['reminder_time'] ?? 24) == 6 ? 'selected' : '' ?>>6 ساعات</option>
                                <option value="12" <?= ($settings['reminder_time'] ?? 24) == 12 ? 'selected' : '' ?>>12 ساعة</option>
                                <option value="24" <?= ($settings['reminder_time'] ?? 24) == 24 ? 'selected' : '' ?>>24 ساعة</option>
                                <option value="48" <?= ($settings['reminder_time'] ?? 24) == 48 ? 'selected' : '' ?>>48 ساعة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-plus me-2 text-danger"></i>
                            تنبيهات المرضى الجدد
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" name="new_patient_alerts" id="new_patient_alerts" value="1" <?= ($settings['new_patient_alerts'] ?? true) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="new_patient_alerts">
                                تفعيل تنبيهات المرضى الجدد
                            </label>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>
                                حفظ الإعدادات
                            </button>
                            <a href="settings.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <!-- يمكن إضافة إعدادات جانبية أو تعليمات هنا -->
            </div>
        </div>
    </form>
</div> 