<div class="container-fluid p-0" style="min-height: 100vh;">
    <div class="row g-0 h-100">
        <!-- Left Side - Login Form -->
        <div class="col-lg-7 d-flex align-items-center justify-content-center" style="padding-top: 80px; padding-bottom: 60px;">
            <div class="w-100" style="max-width: 600px;">
                <div class="text-center mb-4 animate__animated animate__fadeInDown">
                    <div class="mb-3">
                        <i class="fas fa-stethoscope text-primary" style="font-size: 3.5rem;"></i>
                    </div>
                    <h2 class="fw-bold text-primary">مرحباً بعودتك</h2>
                    <p class="text-muted">ادخل إلى لوحة تحكم عيادتك وابدأ إدارة مرضاك</p>
                    <div class="d-inline-flex align-items-center bg-primary text-white px-3 py-1 rounded-pill">
                        <i class="fas fa-shield-alt me-2"></i>
                        <small class="fw-bold">دخول آمن ومشفر</small>
                    </div>
                </div>

                <div class="card shadow-lg border-0 animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                    <div class="card-body p-5">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger animate__animated animate__shakeX">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?= $this->escape($error) ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="login.php" id="loginForm">

                            <div class="mb-3">
                                <label for="email" class="form-label fw-bold">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="fas fa-envelope text-muted"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email"
                                           placeholder="أدخل بريدك الإلكتروني" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label fw-bold">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="fas fa-lock text-muted"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password"
                                           placeholder="أدخل كلمة المرور" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                        <i class="fas fa-eye" id="passwordToggle"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    تذكرني
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg enhanced-login-btn" id="loginBtn">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    دخول إلى لوحة التحكم
                                    <span class="btn-shine"></span>
                                </button>
                            </div>

                            <div class="text-center">
                                <a href="#" class="text-decoration-none">
                                    نسيت كلمة المرور؟
                                </a>
                            </div>
                        </form>

                        <!-- Divider -->
                        <hr class="my-4">

                        <!-- Register Link -->
                        <div class="text-center">
                            <p class="mb-3 text-muted">ليس لديك حساب؟</p>
                            <a href="register.php" class="btn btn-outline-success btn-lg w-100">
                                <i class="fas fa-rocket me-2"></i>
                                ابدأ تجربتك المجانية (15 يوم)
                            </a>
                            <small class="d-block mt-2 text-muted">
                                <i class="fas fa-gift me-1"></i>
                                بدون رسوم إعداد • إلغاء في أي وقت
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Features -->
        <div class="col-lg-5 d-none d-lg-flex align-items-stretch justify-content-center position-relative enhanced-sidebar"
             style="background: linear-gradient(135deg, #007BFF, #0056b3); margin-top: -85px; padding-top: 85px; min-height: calc(100vh + 85px); margin-bottom: -85px; padding-bottom: 85px;">
            <div class="text-center text-white animate__animated animate__fadeInRight d-flex flex-column justify-content-center content" style="padding: 3rem 2rem;">
                <div class="mb-5">
                    <i class="fas fa-user-md" style="font-size: 5rem; opacity: 0.9;"></i>
                </div>
                <h2 class="mb-4 fw-bold">أهلاً بعودتك دكتور</h2>
                <p class="mb-5 fs-5" style="opacity: 0.9;">انضم إلى آلاف الأطباء الذين يثقون بنا</p>

                <div class="row text-center mb-5">
                    <div class="col-12 mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-3 feature-item">
                            <i class="fas fa-shield-alt me-3" style="font-size: 1.5rem;"></i>
                            <span class="fs-5">دخول آمن ومشفر</span>
                        </div>
                    </div>
                    <div class="col-12 mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-3 feature-item">
                            <i class="fas fa-bolt me-3" style="font-size: 1.5rem;"></i>
                            <span class="fs-5">وصول سريع للبيانات</span>
                        </div>
                    </div>
                    <div class="col-12 mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-3 feature-item">
                            <i class="fas fa-user-shield me-3" style="font-size: 1.5rem;"></i>
                            <span class="fs-5">حماية معلومات المرضى</span>
                        </div>
                    </div>
                    <div class="col-12 mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-3 feature-item">
                            <i class="fas fa-clock me-3" style="font-size: 1.5rem;"></i>
                            <span class="fs-5">متاح في أي وقت</span>
                        </div>
                    </div>
                </div>

                <div class="stats-card" style="background: rgba(255,255,255,0.15); border-radius: 20px; padding: 2.5rem; backdrop-filter: blur(10px);">
                    <h4 class="mb-4">
                        <i class="fas fa-chart-line me-3"></i>
                        إحصائيات مذهلة
                    </h4>
                    <div class="row text-center mb-4">
                        <div class="col-6">
                            <h2 class="mb-2 fw-bold">500+</h2>
                            <p class="mb-0 fs-6">عيادة تثق بنا</p>
                        </div>
                        <div class="col-6">
                            <h2 class="mb-2 fw-bold">10K+</h2>
                            <p class="mb-0 fs-6">مريض سعيد</p>
                        </div>
                    </div>
                    <p class="mb-0 fs-6" style="opacity: 0.9;">
                        انضم إلى آلاف الأطباء الذين يديرون عياداتهم بكفاءة وأمان
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const passwordToggle = document.getElementById('passwordToggle');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordToggle.classList.remove('fa-eye');
        passwordToggle.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        passwordToggle.classList.remove('fa-eye-slash');
        passwordToggle.classList.add('fa-eye');
    }
}

// Form submission
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('loginForm');

    form.addEventListener('submit', function(e) {
        // Show loading state
        const loginBtn = document.getElementById('loginBtn');
        const originalText = loginBtn.innerHTML;
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
        loginBtn.disabled = true;

        // Re-enable after 10 seconds as fallback
        setTimeout(() => {
            loginBtn.innerHTML = originalText;
            loginBtn.disabled = false;
        }, 10000);
    });
});
</script>

<style>
/* تحسينات إضافية للتصميم */
.enhanced-sidebar {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.enhanced-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.enhanced-sidebar .content {
    position: relative;
    z-index: 2;
}

.feature-item {
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 10px;
}

.feature-item:hover {
    background: rgba(255,255,255,0.1);
    transform: translateX(-5px);
}

.stats-card {
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* تأكد من أن اللون الأزرق يلتصق بالفوتر */
body {
    overflow-x: hidden;
}

main {
    position: relative;
    z-index: 0;
}

.footer {
    position: relative;
    z-index: 0;
}

@media (max-width: 991.98px) {
    .enhanced-sidebar {
        display: none !important;
    }
}
</style>
