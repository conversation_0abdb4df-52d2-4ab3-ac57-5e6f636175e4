/**
 * ملف الأنماط المخصصة لنظام حكيم
 * Custom styles for Hakim system
 */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #007BFF;
    --secondary-color: #FFFFFF;
    --accent-color: #28A745;
    --text-dark: #333333;
    --text-light: #666666;
    --border-color: #E0E0E0;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 20px rgba(0,0,0,0.15);
}

* {
    font-family: 'Cairo', sans-serif;
}

body {
    background-color: #F8F9FA;
    color: var(--text-dark);
    line-height: 1.6;
}

/* تحسينات Bootstrap */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: var(--shadow-hover);
}

.btn-success {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.card {
    border: none;
    box-shadow: var(--shadow);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--secondary-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
    border-radius: 10px 10px 0 0 !important;
}

/* الجداول */
.table th {
    background-color: #F8F9FA;
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
    color: var(--text-dark);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* النماذج */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* الشارات */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #155724;
    border-left: 4px solid var(--accent-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* كروت الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    width: 220px;
    max-width: 100%;
    min-width: 180px;
    height: 140px;
    margin: 0 auto 20px auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.stats-card.success {
    background: linear-gradient(135deg, var(--accent-color), #1e7e34);
}

.stats-card.warning {
    background: linear-gradient(135deg, #FFC107, #e0a800);
}

.stats-card.danger {
    background: linear-gradient(135deg, #DC3545, #c82333);
}

.stats-card.info {
    background: linear-gradient(135deg, #17A2B8, #138496);
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
    word-break: break-all;
    text-align: center;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* أيقونات الميزات */
.feature-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 24px;
    transition: all 0.3s ease;
}

.feature-icon:hover {
    transform: scale(1.1);
    background-color: #0056b3;
}

/* التحريك */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* حالات التحميل */
.loading {
    display: none;
}

.loading.show {
    display: block;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 2px;
    }
}

/* تحسينات إضافية */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .top-navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin-right: 0 !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background-color: white !important;
    }
}

/* تحسينات إمكانية الوصول */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسينات الأداء */
.card,
.btn,
.form-control {
    will-change: transform;
}

/* إصلاح مشكلة العملة */
.currency-ils::after {
    content: " ₪";
}

/* تحسينات إضافية للعرض */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* تخصيصات إضافية للنظام */
.clinic-logo {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid var(--primary-color);
}

.appointment-status {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.patient-card {
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.patient-card:hover {
    border-left-color: var(--accent-color);
    background-color: rgba(0, 123, 255, 0.02);
}

.prescription-item {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 3px solid var(--accent-color);
}

.invoice-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 20px;
    border-radius: 10px 10px 0 0;
}

/* تحسينات التفاعل */
.clickable {
    cursor: pointer;
    transition: all 0.3s ease;
}

.clickable:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
}

/* تحسينات الألوان للحالات المختلفة */
.status-pending { color: #856404; background-color: rgba(255, 193, 7, 0.1); }
.status-confirmed { color: #0c5460; background-color: rgba(23, 162, 184, 0.1); }
.status-completed { color: #155724; background-color: rgba(40, 167, 69, 0.1); }
.status-cancelled { color: #721c24; background-color: rgba(220, 53, 69, 0.1); }
.status-no-show { color: #495057; background-color: rgba(108, 117, 125, 0.1); }

/* حالات الاشتراك */
.clinic-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.status-active {
    color: #155724;
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-expired {
    color: #721c24;
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.status-expiring {
    color: #856404;
    background-color: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-unknown {
    color: #6c757d;
    background-color: rgba(108, 117, 125, 0.1);
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.row.mb-4 {
    display: flex;
    flex-wrap: wrap;
}

.row.mb-4 > .col-md-3 {
    display: flex;
    justify-content: center;
    align-items: stretch;
}
