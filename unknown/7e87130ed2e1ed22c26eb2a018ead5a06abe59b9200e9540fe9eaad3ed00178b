<?php
/**
 * صفحة إعدادات العيادة
 * Clinic Settings Page
 */
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="fas fa-clinic-medical me-2 text-primary"></i>
        إعدادات العيادة
    </h2>
    <a href="settings.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للإعدادات
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    معلومات العيادة
                </h5>
            </div>
            <div class="card-body">
                <?php if (isset($_SESSION['notification'])): ?>
                    <div class="alert alert-<?= $_SESSION['notification']['type'] ?> alert-dismissible fade show" role="alert">
                        <?= $_SESSION['notification']['message'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['notification']); ?>
                <?php endif; ?>
                
                <form method="POST" id="clinicForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم العيادة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?= $clinic['name'] ?? '' ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="specialization" class="form-label">التخصص <span class="text-danger">*</span></label>
                            <select class="form-select" id="specialization" name="specialization" required>
                                <option value="">اختر التخصص</option>
                                <option value="طب عام" <?= ($clinic['specialization'] ?? '') == 'طب عام' ? 'selected' : '' ?>>طب عام</option>
                                <option value="طب أطفال" <?= ($clinic['specialization'] ?? '') == 'طب أطفال' ? 'selected' : '' ?>>طب أطفال</option>
                                <option value="طب نساء وولادة" <?= ($clinic['specialization'] ?? '') == 'طب نساء وولادة' ? 'selected' : '' ?>>طب نساء وولادة</option>
                                <option value="طب عيون" <?= ($clinic['specialization'] ?? '') == 'طب عيون' ? 'selected' : '' ?>>طب عيون</option>
                                <option value="طب أسنان" <?= ($clinic['specialization'] ?? '') == 'طب أسنان' ? 'selected' : '' ?>>طب أسنان</option>
                                <option value="طب جلدية" <?= ($clinic['specialization'] ?? '') == 'طب جلدية' ? 'selected' : '' ?>>طب جلدية</option>
                                <option value="طب قلب" <?= ($clinic['specialization'] ?? '') == 'طب قلب' ? 'selected' : '' ?>>طب قلب</option>
                                <option value="طب عظام" <?= ($clinic['specialization'] ?? '') == 'طب عظام' ? 'selected' : '' ?>>طب عظام</option>
                                <option value="طب نفسي" <?= ($clinic['specialization'] ?? '') == 'طب نفسي' ? 'selected' : '' ?>>طب نفسي</option>
                                <option value="أخرى" <?= ($clinic['specialization'] ?? '') == 'أخرى' ? 'selected' : '' ?>>أخرى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?= $clinic['phone'] ?? '' ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?= $clinic['email'] ?? '' ?>">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3" 
                                  placeholder="العنوان الكامل للعيادة..."><?= $clinic['address'] ?? '' ?></textarea>
                    </div>
                    
                    <hr class="my-4">
                    
                    <h6 class="mb-3">
                        <i class="fas fa-clock me-2 text-primary"></i>
                        ساعات العمل
                    </h6>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="working_hours_start" class="form-label">بداية العمل <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" id="working_hours_start" name="working_hours_start" 
                                   value="<?= $clinic['working_hours_start'] ?? '09:00' ?>" required>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="working_hours_end" class="form-label">نهاية العمل <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" id="working_hours_end" name="working_hours_end" 
                                   value="<?= $clinic['working_hours_end'] ?? '17:00' ?>" required>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="appointment_duration" class="form-label">مدة الموعد (دقيقة) <span class="text-danger">*</span></label>
                            <select class="form-select" id="appointment_duration" name="appointment_duration" required>
                                <option value="15" <?= ($clinic['appointment_duration'] ?? 30) == 15 ? 'selected' : '' ?>>15 دقيقة</option>
                                <option value="30" <?= ($clinic['appointment_duration'] ?? 30) == 30 ? 'selected' : '' ?>>30 دقيقة</option>
                                <option value="45" <?= ($clinic['appointment_duration'] ?? 30) == 45 ? 'selected' : '' ?>>45 دقيقة</option>
                                <option value="60" <?= ($clinic['appointment_duration'] ?? 30) == 60 ? 'selected' : '' ?>>60 دقيقة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="settings.php" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الإعدادات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- معلومات العيادة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات العيادة
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar mx-auto mb-3">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" 
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-clinic-medical fa-2x"></i>
                        </div>
                    </div>
                    <h5><?= $clinic['name'] ?? 'اسم العيادة' ?></h5>
                    <p class="text-muted"><?= $clinic['specialization'] ?? 'التخصص' ?></p>
                </div>
                
                <hr>
                
                <div class="mb-2">
                    <small class="text-muted">الهاتف:</small>
                    <div><?= $clinic['phone'] ?? 'غير محدد' ?></div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">البريد الإلكتروني:</small>
                    <div><?= $clinic['email'] ?? 'غير محدد' ?></div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">ساعات العمل:</small>
                    <div><?= ($clinic['working_hours_start'] ?? '09:00') . ' - ' . ($clinic['working_hours_end'] ?? '17:00') ?></div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">مدة الموعد:</small>
                    <div><?= ($clinic['appointment_duration'] ?? 30) ?> دقيقة</div>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">0</h4>
                            <small class="text-muted">المرضى</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-success mb-1">0</h4>
                            <small class="text-muted">المواعيد</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h4 class="text-info mb-1">0</h4>
                        <small class="text-muted">الجلسات</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- نصائح -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>تأكد من صحة معلومات الاتصال</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>حدد ساعات العمل بدقة</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>اختر مدة مناسبة للمواعيد</small>
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>راجع الإعدادات دورياً</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('clinicForm');
    const phoneInput = document.getElementById('phone');
    const nameInput = document.getElementById('name');
    
    // تنسيق رقم الهاتف
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 0) {
            if (value.startsWith('966')) {
                value = '+' + value;
            } else if (value.startsWith('05')) {
                value = '+966' + value.substring(1);
            } else if (value.startsWith('5')) {
                value = '+966' + value;
            }
        }
        this.value = value;
    });
    
    // تحويل اسم العيادة للأحرف الكبيرة
    nameInput.addEventListener('blur', function() {
        this.value = this.value.replace(/\b\w/g, l => l.toUpperCase());
    });
    
    // التحقق من صحة النموذج
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
    
    // تحسين تجربة المستخدم
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.borderColor = 'var(--primary-blue)';
        });
        
        input.addEventListener('blur', function() {
            this.style.borderColor = '';
        });
    });
});

function validateForm() {
    const name = document.getElementById('name').value.trim();
    const phone = document.getElementById('phone').value.trim();
    const specialization = document.getElementById('specialization').value;
    const workStart = document.getElementById('working_hours_start').value;
    const workEnd = document.getElementById('working_hours_end').value;
    
    if (!name) {
        alert('يرجى إدخال اسم العيادة');
        document.getElementById('name').focus();
        return false;
    }
    
    if (name.length < 3) {
        alert('يجب أن يكون اسم العيادة 3 أحرف على الأقل');
        document.getElementById('name').focus();
        return false;
    }
    
    if (!phone) {
        alert('يرجى إدخال رقم الهاتف');
        document.getElementById('phone').focus();
        return false;
    }
    
    if (!specialization) {
        alert('يرجى اختيار التخصص');
        document.getElementById('specialization').focus();
        return false;
    }
    
    if (!workStart || !workEnd) {
        alert('يرجى تحديد ساعات العمل');
        return false;
    }
    
    if (workStart >= workEnd) {
        alert('وقت بداية العمل يجب أن يكون قبل وقت النهاية');
        document.getElementById('working_hours_start').focus();
        return false;
    }
    
    return true;
}
</script>
