<?php
/**
 * صفحة تغيير كلمة المرور
 * Change Password Page
 */
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
        <i class="fas fa-key me-2 text-primary"></i>
        تغيير كلمة المرور
    </h2>
    <a href="settings.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للإعدادات
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    تحديث كلمة المرور
                </h5>
            </div>
            <div class="card-body">
                <?php if (isset($_SESSION['notification'])): ?>
                    <div class="alert alert-<?= $_SESSION['notification']['type'] ?> alert-dismissible fade show" role="alert">
                        <?= $_SESSION['notification']['message'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['notification']); ?>
                <?php endif; ?>
                
                <form method="POST" id="passwordForm">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">كلمة المرور الحالية <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                <i class="fas fa-eye" id="current_password_icon"></i>
                            </button>
                        </div>
                        <div class="form-text">أدخل كلمة المرور الحالية للتأكد من هويتك</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye" id="new_password_icon"></i>
                            </button>
                        </div>
                        <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                        <div class="password-strength mt-2">
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" id="strength-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small id="strength-text" class="text-muted">قوة كلمة المرور</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye" id="confirm_password_icon"></i>
                            </button>
                        </div>
                        <div id="password-match" class="form-text"></div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>متطلبات كلمة المرور:</h6>
                        <ul class="mb-0">
                            <li>6 أحرف على الأقل</li>
                            <li>تحتوي على أحرف وأرقام</li>
                            <li>تجنب استخدام معلومات شخصية</li>
                            <li>لا تستخدم كلمات مرور سابقة</li>
                        </ul>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="settings.php" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary" id="submit-btn" disabled>
                            <i class="fas fa-save me-2"></i>تحديث كلمة المرور
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- نصائح الأمان -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    نصائح الأمان
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-3">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong>استخدم كلمة مرور قوية</strong>
                        <br><small class="text-muted">امزج بين الأحرف والأرقام والرموز</small>
                    </li>
                    <li class="mb-3">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong>لا تشارك كلمة المرور</strong>
                        <br><small class="text-muted">احتفظ بكلمة المرور سرية</small>
                    </li>
                    <li class="mb-3">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong>غير كلمة المرور دورياً</strong>
                        <br><small class="text-muted">ننصح بتغييرها كل 3-6 أشهر</small>
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong>استخدم كلمة مرور مختلفة</strong>
                        <br><small class="text-muted">لا تستخدم نفس كلمة المرور في مواقع أخرى</small>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted small mb-2">
                    <i class="fas fa-clock me-1"></i>
                    آخر تغيير لكلمة المرور: منذ 30 يوماً
                </p>
                <p class="text-muted small mb-2">
                    <i class="fas fa-history me-1"></i>
                    سيتم تسجيل خروجك من جميع الأجهزة بعد تغيير كلمة المرور
                </p>
                <p class="text-muted small mb-0">
                    <i class="fas fa-envelope me-1"></i>
                    سيتم إرسال تأكيد على بريدك الإلكتروني
                </p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const currentPassword = document.getElementById('current_password');
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    const submitBtn = document.getElementById('submit-btn');
    const strengthBar = document.getElementById('strength-bar');
    const strengthText = document.getElementById('strength-text');
    const passwordMatch = document.getElementById('password-match');
    
    // تحقق من قوة كلمة المرور
    newPassword.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        updatePasswordStrength(strength);
        checkFormValidity();
    });
    
    // تحقق من تطابق كلمة المرور
    confirmPassword.addEventListener('input', function() {
        checkPasswordMatch();
        checkFormValidity();
    });
    
    currentPassword.addEventListener('input', checkFormValidity);
    
    function calculatePasswordStrength(password) {
        let score = 0;
        
        if (password.length >= 6) score += 20;
        if (password.length >= 8) score += 20;
        if (/[a-z]/.test(password)) score += 20;
        if (/[A-Z]/.test(password)) score += 20;
        if (/[0-9]/.test(password)) score += 20;
        if (/[^A-Za-z0-9]/.test(password)) score += 20;
        
        return Math.min(score, 100);
    }
    
    function updatePasswordStrength(strength) {
        strengthBar.style.width = strength + '%';
        
        if (strength < 40) {
            strengthBar.className = 'progress-bar bg-danger';
            strengthText.textContent = 'ضعيفة';
            strengthText.className = 'text-danger';
        } else if (strength < 70) {
            strengthBar.className = 'progress-bar bg-warning';
            strengthText.textContent = 'متوسطة';
            strengthText.className = 'text-warning';
        } else {
            strengthBar.className = 'progress-bar bg-success';
            strengthText.textContent = 'قوية';
            strengthText.className = 'text-success';
        }
    }
    
    function checkPasswordMatch() {
        const newPass = newPassword.value;
        const confirmPass = confirmPassword.value;
        
        if (confirmPass.length > 0) {
            if (newPass === confirmPass) {
                passwordMatch.innerHTML = '<i class="fas fa-check text-success me-1"></i>كلمة المرور متطابقة';
                passwordMatch.className = 'form-text text-success';
            } else {
                passwordMatch.innerHTML = '<i class="fas fa-times text-danger me-1"></i>كلمة المرور غير متطابقة';
                passwordMatch.className = 'form-text text-danger';
            }
        } else {
            passwordMatch.innerHTML = '';
        }
    }
    
    function checkFormValidity() {
        const isValid = currentPassword.value.length > 0 &&
                       newPassword.value.length >= 6 &&
                       confirmPassword.value === newPassword.value;
        
        submitBtn.disabled = !isValid;
    }
    
    // التحقق من صحة النموذج عند الإرسال
    document.getElementById('passwordForm').addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
});

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

function validateForm() {
    const currentPassword = document.getElementById('current_password').value;
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (!currentPassword) {
        alert('يرجى إدخال كلمة المرور الحالية');
        return false;
    }
    
    if (newPassword.length < 6) {
        alert('يجب أن تكون كلمة المرور الجديدة 6 أحرف على الأقل');
        return false;
    }
    
    if (newPassword !== confirmPassword) {
        alert('كلمة المرور الجديدة وتأكيدها غير متطابقين');
        return false;
    }
    
    if (currentPassword === newPassword) {
        alert('كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية');
        return false;
    }
    
    return confirm('هل أنت متأكد من تغيير كلمة المرور؟\nسيتم تسجيل خروجك من جميع الأجهزة.');
}
</script>
