<?php
/**
 * صفحة الإعدادات
 * Settings Page
 */

// تشغيل الجلسات
session_start();

// تعريف المسارات
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('VIEWS_PATH', APP_PATH . '/views');

// تحميل الإعدادات
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/models/User.php';
require_once APP_PATH . '/models/Clinic.php';
require_once APP_PATH . '/controllers/SettingsController.php';
require_once APP_PATH . '/core/AuthGuard.php';
requireAuth();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// تشغيل كونترولر الإعدادات
$controller = new SettingsController();
$action = $_GET['action'] ?? 'index';

switch ($action) {
    case 'index':
        $controller->index();
        break;
    case 'clinic':
        $controller->clinic();
        break;
    case 'profile':
        $controller->profile();
        break;
    case 'password':
        $controller->password();
        break;
    case 'appointments':
        $controller->appointments();
        break;
    case 'notifications':
        $controller->notifications();
        break;
    case 'backup':
        $controller->backup();
        break;
    default:
        $controller->index();
        break;
}
?>
