<?php
/**
 * إضافة فاتورة جديدة
 * Add New Invoice
 */
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        إضافة فاتورة جديدة
                    </h4>
                    <a href="invoices.php" class="btn btn-secondary btn-sm float-end">
                        <i class="fas fa-arrow-right"></i>
                        العودة للفواتير
                    </a>
                </div>
                <div class="card-body">
                    <form method="POST" action="invoices.php?action=add">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="patient_id">المريض <span class="text-danger">*</span></label>
                                    <select name="patient_id" id="patient_id" class="form-control" required>
                                        <option value="">اختر المريض</option>
                                        <?php foreach ($patients as $patient): ?>
                                            <option value="<?= $patient['id'] ?>">
                                                <?= htmlspecialchars($patient['full_name']) ?> - <?= $patient['phone'] ?> 
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="invoice_date">تاريخ الفاتورة <span class="text-danger">*</span></label>
                                    <input type="date" name="invoice_date" id="invoice_date" class="form-control" value="<?= date('Y-m-d') ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="services">الخدمات <span class="text-danger">*</span></label>
                            <textarea name="services" id="services" class="form-control" rows="3" placeholder="أدخل الخدمات المقدمة (مثال: كشف - تحليل - أشعة)" required></textarea>
                            <small class="form-text text-muted">يمكنك إدخال أكثر من خدمة مفصولة بفاصلة أو استخدام جدول ديناميكي لاحقاً.</small>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="subtotal">الإجمالي الفرعي</label>
                                    <input type="number" step="0.01" name="subtotal" id="subtotal" class="form-control" placeholder="0.00" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="discount">الخصم</label>
                                    <input type="number" step="0.01" name="discount" id="discount" class="form-control" placeholder="0.00">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="total_amount">المبلغ الإجمالي <span class="text-danger">*</span></label>
                                    <input type="number" step="0.01" name="total_amount" id="total_amount" class="form-control" placeholder="0.00" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="notes">ملاحظات</label>
                            <textarea name="notes" id="notes" class="form-control" rows="2" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                حفظ الفاتورة
                            </button>
                            <a href="invoices.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// حساب المبلغ الإجمالي تلقائياً عند تغيير الإجمالي الفرعي أو الخصم
const subtotalInput = document.getElementById('subtotal');
const discountInput = document.getElementById('discount');
const totalInput = document.getElementById('total_amount');

function updateTotal() {
    const subtotal = parseFloat(subtotalInput.value) || 0;
    const discount = parseFloat(discountInput.value) || 0;
    totalInput.value = (subtotal - discount).toFixed(2);
}

subtotalInput.addEventListener('input', updateTotal);
discountInput.addEventListener('input', updateTotal);
</script> 