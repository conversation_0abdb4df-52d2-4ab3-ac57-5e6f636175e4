<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left Side - Forgot Password Form -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center">
            <div class="w-100" style="max-width: 450px;">
                <div class="text-center mb-4 animate__animated animate__fadeInDown">
                    <div class="mb-3">
                        <i class="fas fa-key text-primary" style="font-size: 3.5rem;"></i>
                    </div>
                    <h2 class="fw-bold text-primary">نسيت كلمة المرور؟</h2>
                    <p class="text-muted">أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة التعيين</p>
                </div>
                
                <div class="card shadow-lg border-0 animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                    <div class="card-body p-4">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger animate__animated animate__shakeX">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?= $this->escape($error) ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" id="forgotForm">
                            <?= $this->csrfField() ?>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label fw-bold">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="fas fa-envelope text-muted"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           placeholder="أدخل بريدك الإلكتروني" required>
                                </div>
                            </div>
                            
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg enhanced-login-btn">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال رابط إعادة التعيين
                                    <span class="btn-shine"></span>
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center">
                            <p class="mb-0 text-muted">تذكرت كلمة المرور؟ 
                                <a href="login.php" class="text-primary text-decoration-none fw-bold">
                                    تسجيل الدخول
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Side - Info -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center" 
             style="background: linear-gradient(135deg, #6f42c1, #e83e8c);">
            <div class="text-center text-white animate__animated animate__fadeInRight">
                <div class="mb-4">
                    <i class="fas fa-shield-alt" style="font-size: 4rem; opacity: 0.9;"></i>
                </div>
                <h3 class="mb-4">استعادة آمنة</h3>
                
                <div class="row text-center mb-4">
                    <div class="col-12 mb-3">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-check-circle me-2" style="font-size: 1.2rem;"></i>
                            <span>عملية آمنة ومشفرة</span>
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-check-circle me-2" style="font-size: 1.2rem;"></i>
                            <span>رابط صالح لساعة واحدة</span>
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-check-circle me-2" style="font-size: 1.2rem;"></i>
                            <span>حماية بياناتك</span>
                        </div>
                    </div>
                </div>
                
                <div style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 2rem;">
                    <h5 class="mb-3">نظام آمن</h5>
                    <p class="mb-0" style="font-size: 0.9rem; opacity: 0.9;">
                        نحن نحمي بياناتك بأعلى معايير الأمان والخصوصية
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const forgotForm = document.getElementById('forgotForm');
    
    forgotForm.addEventListener('submit', function(e) {
        const email = document.getElementById('email').value;
        
        if (!email) {
            e.preventDefault();
            alert('يرجى إدخال البريد الإلكتروني');
            return false;
        }
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
        submitBtn.disabled = true;
        
        // Re-enable after 10 seconds as fallback
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 10000);
    });
});
</script>
