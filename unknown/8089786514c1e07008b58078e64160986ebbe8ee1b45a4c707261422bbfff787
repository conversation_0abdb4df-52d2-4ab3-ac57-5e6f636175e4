<?php
/**
 * موديل الجلسات الطبية
 * Medical Session Model
 */

class MedicalSession extends Model {
    protected $table = 'sessions';
    
    /**
     * الحصول على الجلسات حسب العيادة
     * Get sessions by clinic
     */
    public function getSessionsByClinic($clinicId, $filter = 'all', $date = null) {
        $sql = "SELECT s.*, p.full_name as patient_name, p.phone as patient_phone, p.date_of_birth
                FROM {$this->table} s
                LEFT JOIN patients p ON s.patient_id = p.id
                WHERE s.clinic_id = ?";
        
        $params = [$clinicId];
        
        // تطبيق الفلاتر
        if ($filter !== 'all') {
            $sql .= " AND s.status = ?";
            $params[] = $filter;
        }
        
        if ($date) {
            $sql .= " AND s.session_date = ?";
            $params[] = $date;
        }
        
        $sql .= " ORDER BY s.session_date DESC, s.session_time DESC";
        
        return $this->db->query($sql, $params)->fetchAll();
    }
    
    /**
     * الحصول على تفاصيل الجلسة
     * Get session details
     */
    public function getSessionDetails($id) {
        $sql = "SELECT s.*, p.full_name as patient_name, p.phone as patient_phone, 
                       p.email as patient_email, p.date_of_birth, p.gender,
                       p.medical_history, p.allergies,
                       u.full_name as doctor_name
                FROM {$this->table} s
                LEFT JOIN patients p ON s.patient_id = p.id
                LEFT JOIN users u ON s.created_by = u.id
                WHERE s.id = ?";
        
        return $this->db->query($sql, [$id])->fetch();
    }
    
    /**
     * الحصول على جلسات اليوم
     * Get today's sessions
     */
    public function getTodaySessions($clinicId) {
        $sql = "SELECT s.*, p.full_name as patient_name, p.phone as patient_phone
                FROM {$this->table} s
                LEFT JOIN patients p ON s.patient_id = p.id
                WHERE s.clinic_id = ? AND s.session_date = CURDATE()
                ORDER BY s.session_time ASC";
        
        return $this->db->query($sql, [$clinicId])->fetchAll();
    }
    
    /**
     * الحصول على الجلسات حسب المريض
     * Get sessions by patient
     */
    public function getSessionsByPatient($patientId) {
        $sql = "SELECT s.*, c.name as clinic_name, u.full_name as doctor_name
                FROM {$this->table} s
                LEFT JOIN clinics c ON s.clinic_id = c.id
                LEFT JOIN users u ON s.created_by = u.id
                WHERE s.patient_id = ?
                ORDER BY s.session_date DESC, s.session_time DESC";
        
        return $this->db->query($sql, [$patientId])->fetchAll();
    }
    
    /**
     * البحث في الجلسات
     * Search sessions
     */
    public function search($clinicId, $query) {
        $sql = "SELECT s.*, p.full_name as patient_name, p.phone as patient_phone
                FROM {$this->table} s
                LEFT JOIN patients p ON s.patient_id = p.id
                WHERE s.clinic_id = ? AND (
                    p.full_name LIKE ? OR 
                    p.phone LIKE ? OR 
                    s.chief_complaint LIKE ? OR
                    s.diagnosis LIKE ?
                )
                ORDER BY s.session_date DESC, s.session_time DESC";
        
        $searchTerm = '%' . $query . '%';
        return $this->db->query($sql, [$clinicId, $searchTerm, $searchTerm, $searchTerm, $searchTerm])->fetchAll();
    }
    
    /**
     * الحصول على إحصائيات الجلسات
     * Get session statistics
     */
    public function getStats($clinicId) {
        $stats = [];
        
        // إجمالي الجلسات هذا الشهر
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE clinic_id = ? AND MONTH(session_date) = MONTH(CURDATE()) AND YEAR(session_date) = YEAR(CURDATE())";
        $result = $this->db->query($sql, [$clinicId]);
        $stats['total_this_month'] = $result && ($row = $result->fetch()) ? $row['count'] : 0;
        
        // جلسات اليوم
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE clinic_id = ? AND session_date = CURDATE()";
        $result = $this->db->query($sql, [$clinicId]);
        $stats['today'] = $result && ($row = $result->fetch()) ? $row['count'] : 0;
        
        return $stats;
    }
    
    /**
     * الحصول على آخر جلسة للمريض
     * Get patient's last session
     */
    public function getLastSessionByPatient($patientId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE patient_id = ?
                ORDER BY session_date DESC, session_time DESC
                LIMIT 1";
        
        return $this->db->query($sql, [$patientId])->fetch();
    }
    
    /**
     * الحصول على الجلسات التي تحتاج متابعة
     * Get sessions that need follow-up
     */
    public function getFollowUpSessions($clinicId) {
        $sql = "SELECT s.*, p.full_name as patient_name, p.phone as patient_phone
                FROM {$this->table} s
                LEFT JOIN patients p ON s.patient_id = p.id
                WHERE s.clinic_id = ? AND s.follow_up_date <= CURDATE() AND s.follow_up_date IS NOT NULL
                ORDER BY s.follow_up_date ASC";
        
        return $this->db->query($sql, [$clinicId])->fetchAll();
    }
    
    /**
     * إنشاء جلسة جديدة
     * Create new session
     */
    public function create($data) {
        $fields = ['appointment_id', 'patient_id', 'clinic_id', 'doctor_id', 'session_date', 'session_time', 
                   'diagnosis', 'treatment_plan', 'notes', 'amount', 'payment_status', 'created_at'];
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        
        $sql = "INSERT INTO {$this->table} (" . implode(',', $fields) . ") VALUES ($placeholders)";
        
        $values = [];
        foreach ($fields as $field) {
            $values[] = $data[$field] ?? null;
        }
        
        return $this->db->query($sql, $values);
    }
    
    /**
     * تحديث الجلسة
     * Update session
     */
    public function update($id, $data) {
        $fields = ['appointment_id', 'patient_id', 'doctor_id', 'session_date', 'session_time', 
                   'diagnosis', 'treatment_plan', 'notes', 'amount', 'payment_status', 'updated_at'];
        $setClause = implode(' = ?, ', $fields) . ' = ?';
        
        $sql = "UPDATE {$this->table} SET $setClause WHERE id = ?";
        
        $values = [];
        foreach ($fields as $field) {
            $values[] = $data[$field] ?? null;
        }
        $values[] = $id;
        
        return $this->db->query($sql, $values);
    }
    
    /**
     * حذف الجلسة
     * Delete session
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE id = ?";
        return $this->db->query($sql, [$id]);
    }
    
    /**
     * البحث عن جلسة
     * Find session
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE id = ?";
        return $this->db->query($sql, [$id])->fetch();
    }
    
    /**
     * الحصول على التشخيصات الأكثر شيوعاً
     * Get most common diagnoses
     */
    public function getMostCommonDiagnoses($clinicId, $limit = 10) {
        $limit = (int) $limit;
        if ($limit <= 0) $limit = 10;
        $sql = "SELECT diagnosis, COUNT(*) as count
                FROM {$this->table}
                WHERE clinic_id = ? AND diagnosis IS NOT NULL AND diagnosis != ''
                GROUP BY diagnosis
                ORDER BY count DESC
                LIMIT $limit";
        return $this->db->query($sql, [$clinicId])->fetchAll();
    }
    
    /**
     * الحصول على الأدوية الأكثر وصفاً
     * Get most prescribed medications
     */
    public function getMostPrescribedMedications($clinicId, $limit = 10) {
        $limit = (int) $limit;
        if ($limit <= 0) $limit = 10;
        $sql = "SELECT medications, COUNT(*) as count
                FROM {$this->table}
                WHERE clinic_id = ? AND medications IS NOT NULL AND medications != ''
                GROUP BY medications
                ORDER BY count DESC
                LIMIT $limit";
        return $this->db->query($sql, [$clinicId])->fetchAll();
    }
    
    /**
     * الحصول على إحصائيات شهرية
     * Get monthly statistics
     */
    public function getMonthlyStats($clinicId, $year = null) {
        $year = $year ?? date('Y');
        
        $sql = "SELECT 
                    MONTH(session_date) as month,
                    COUNT(*) as total_sessions,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sessions
                FROM {$this->table}
                WHERE clinic_id = ? AND YEAR(session_date) = ?
                GROUP BY MONTH(session_date)
                ORDER BY month";
        
        return $this->db->query($sql, [$clinicId, $year])->fetchAll();
    }
}
?>
