RewriteEngine On

# Handle specific routes first
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Dashboard routes
RewriteRule ^dashboard/?$ dashboard.php [L]
RewriteRule ^appointments/?$ appointments.php [L]
RewriteRule ^patients/?$ patients.php [L]
RewriteRule ^sessions/?$ sessions.php [L]
RewriteRule ^prescriptions/?$ prescriptions.php [L]
RewriteRule ^invoices/?$ invoices.php [L]
RewriteRule ^reports/?$ reports.php [L]
RewriteRule ^settings/?$ settings.php [L]
RewriteRule ^profile/?$ profile.php [L]
RewriteRule ^logout/?$ logout.php [L]

# Auth routes
RewriteRule ^login/?$ login.php [L]
RewriteRule ^register/?$ register.php [L]

# Public routes
RewriteRule ^contact/?$ contact.php [L]
RewriteRule ^features/?$ features.php [L]
RewriteRule ^pricing/?$ pricing.php [L]

# Default route to index for other URLs
RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]


