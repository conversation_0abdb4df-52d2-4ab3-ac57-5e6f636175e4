<?php
/**
 * كونترولر لوحة التحكم
 * Dashboard Controller
 */

class DashboardController extends Controller {
    
    /**
     * الصفحة الرئيسية للوحة التحكم
     * Main dashboard page
     */
    public function index() {
        $this->requireAuth();
        
        // الحصول على بيانات المستخدم والعيادة
        $userModel = $this->model('User');
        $clinicModel = $this->model('Clinic');
        
        $user = $userModel->find($_SESSION['user_id']);
        $clinic = null;
        $stats = [];
        
        if ($user['clinic_id']) {
            $clinic = $clinicModel->find($user['clinic_id']);
            $stats = $clinicModel->getClinicStats($user['clinic_id']);
        }
        
        // الحصول على المواعيد اليوم
        $todayAppointments = [];
        if ($user['clinic_id']) {
            $sql = "SELECT a.*, p.full_name as patient_name 
                    FROM appointments a 
                    LEFT JOIN patients p ON a.patient_id = p.id 
                    WHERE a.clinic_id = ? AND a.appointment_date = CURDATE() 
                    ORDER BY a.appointment_time";
            $todayAppointments = $this->db->select($sql, [$user['clinic_id']]);
        }
        
        // الحصول على الإشعارات
        $notifications = [];
        if ($user['clinic_id']) {
            $sql = "SELECT * FROM notifications 
                    WHERE clinic_id = ? AND is_read = 0 
                    ORDER BY created_at DESC LIMIT 5";
            $notifications = $this->db->select($sql, [$user['clinic_id']]);
        }
        
        $data = [
            'title' => 'لوحة التحكم - نظام حكيم',
            'user' => $user,
            'clinic' => $clinic,
            'stats' => $stats,
            'today_appointments' => $todayAppointments,
            'notifications' => $notifications
        ];
        
        $this->view->renderWithLayout('dashboard/index', 'dashboard', $data);
    }
    
    /**
     * صفحة الإعدادات
     * Settings page
     */
    public function settings() {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleSettings();
        }
        
        $userModel = $this->model('User');
        $clinicModel = $this->model('Clinic');
        
        $user = $userModel->find($_SESSION['user_id']);
        $clinic = null;
        
        if ($user['clinic_id']) {
            $clinic = $clinicModel->find($user['clinic_id']);
        }
        
        $data = [
            'title' => 'الإعدادات',
            'user' => $user,
            'clinic' => $clinic
        ];
        
        $this->view->renderWithLayout('dashboard/settings', 'dashboard', $data);
    }
    
    /**
     * معالجة تحديث الإعدادات
     * Handle settings update
     */
    private function handleSettings() {
        $data = $this->sanitize($_POST);
        
        $userModel = $this->model('User');
        $clinicModel = $this->model('Clinic');
        
        // تحديث بيانات المستخدم
        if (isset($data['user_data'])) {
            $userData = $data['user_data'];
            
            // إزالة كلمة المرور إذا كانت فارغة
            if (empty($userData['password'])) {
                unset($userData['password']);
            }
            
            $userModel->updateProfile($_SESSION['user_id'], $userData);
            $this->notify('تم تحديث بيانات المستخدم بنجاح', 'success');
        }
        
        // تحديث بيانات العيادة
        if (isset($data['clinic_data']) && $_SESSION['clinic_id']) {
            $clinicData = $data['clinic_data'];
            $clinicModel->update($_SESSION['clinic_id'], $clinicData);
            $this->notify('تم تحديث بيانات العيادة بنجاح', 'success');
        }
        
        $this->redirect('dashboard/settings');
    }
    
    /**
     * صفحة الملف الشخصي
     * Profile page
     */
    public function profile() {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleProfileUpdate();
        }
        
        $userModel = $this->model('User');
        $user = $userModel->find($_SESSION['user_id']);
        
        $data = [
            'title' => 'الملف الشخصي',
            'user' => $user
        ];
        
        $this->view->renderWithLayout('dashboard/profile', 'dashboard', $data);
    }
    
    /**
     * معالجة تحديث الملف الشخصي
     * Handle profile update
     */
    private function handleProfileUpdate() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'full_name' => 'required|min:3',
            'email' => 'required|email',
            'phone' => 'required|phone'
        ]);
        
        if (empty($errors)) {
            $userModel = $this->model('User');
            
            // التحقق من عدم وجود البريد الإلكتروني لمستخدم آخر
            if ($userModel->emailExists($data['email'], $_SESSION['user_id'])) {
                $this->notify('البريد الإلكتروني مستخدم بالفعل', 'error');
                return;
            }
            
            $updated = $userModel->updateProfile($_SESSION['user_id'], $data);
            
            if ($updated) {
                // تحديث بيانات الجلسة
                $_SESSION['user_name'] = $data['full_name'];
                $_SESSION['user_email'] = $data['email'];
                
                $this->notify('تم تحديث الملف الشخصي بنجاح', 'success');
            } else {
                $this->notify('حدث خطأ أثناء التحديث', 'error');
            }
        } else {
            $this->notify('يرجى تصحيح الأخطاء في النموذج', 'error');
        }
    }
    
    /**
     * صفحة الإحصائيات
     * Statistics page
     */
    public function stats() {
        $this->requireAuth();
        
        if (!$_SESSION['clinic_id']) {
            $this->notify('لا توجد عيادة مرتبطة بحسابك', 'error');
            $this->redirect('dashboard');
        }
        
        $clinicModel = $this->model('Clinic');
        $stats = $clinicModel->getClinicStats($_SESSION['clinic_id']);
        
        // إحصائيات إضافية
        $monthlyStats = $this->getMonthlyStats();
        $yearlyStats = $this->getYearlyStats();
        
        $data = [
            'title' => 'الإحصائيات',
            'stats' => $stats,
            'monthly_stats' => $monthlyStats,
            'yearly_stats' => $yearlyStats
        ];
        
        $this->view->renderWithLayout('dashboard/stats', 'dashboard', $data);
    }
    
    /**
     * الحصول على إحصائيات شهرية
     * Get monthly statistics
     */
    private function getMonthlyStats() {
        $sql = "SELECT 
                    MONTH(appointment_date) as month,
                    COUNT(*) as appointments_count,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
                    SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as total_income
                FROM appointments 
                WHERE clinic_id = ? AND YEAR(appointment_date) = YEAR(CURDATE())
                GROUP BY MONTH(appointment_date)
                ORDER BY month";
        
        return $this->db->select($sql, [$_SESSION['clinic_id']]);
    }
    
    /**
     * الحصول على إحصائيات سنوية
     * Get yearly statistics
     */
    private function getYearlyStats() {
        $sql = "SELECT 
                    YEAR(appointment_date) as year,
                    COUNT(*) as appointments_count,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
                    SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as total_income
                FROM appointments 
                WHERE clinic_id = ?
                GROUP BY YEAR(appointment_date)
                ORDER BY year DESC
                LIMIT 5";
        
        return $this->db->select($sql, [$_SESSION['clinic_id']]);
    }
}
?>
