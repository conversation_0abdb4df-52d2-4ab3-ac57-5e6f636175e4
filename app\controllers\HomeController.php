<?php
/**
 * كونترولر الصفحة الرئيسية
 * Home Controller
 */

class HomeController extends Controller {
    
    /**
     * الصفحة الرئيسية للموقع العام
     * Main landing page
     */
    public function index() {
        $data = [
            'title' => 'حكيم - نظام إدارة العيادات الطبية',
            'description' => 'نظام شامل لإدارة العيادات الطبية مع ميزات متقدمة للحجز والمتابعة',
            'features' => [
                [
                    'icon' => 'fas fa-calendar-check',
                    'title' => 'إدارة المواعيد',
                    'description' => 'نظام ذكي لإدارة المواعيد مع إمكانية الحجز المباشر'
                ],
                [
                    'icon' => 'fas fa-users',
                    'title' => 'إدارة المرضى',
                    'description' => 'ملفات شاملة للمرضى مع التاريخ الطبي والوصفات'
                ],
                [
                    'icon' => 'fas fa-prescription-bottle-alt',
                    'title' => 'الوصفات الإلكترونية',
                    'description' => 'إنشاء وطباعة الوصفات الطبية بسهولة'
                ],
                [
                    'icon' => 'fas fa-chart-line',
                    'title' => 'التقارير والإحصائيات',
                    'description' => 'تقارير مفصلة عن الدخل والمرضى والأداء'
                ],
                [
                    'icon' => 'fas fa-mobile-alt',
                    'title' => 'تصميم متجاوب',
                    'description' => 'يعمل على جميع الأجهزة والشاشات'
                ],
                [
                    'icon' => 'fas fa-shield-alt',
                    'title' => 'أمان عالي',
                    'description' => 'حماية متقدمة لبيانات المرضى والعيادة'
                ]
            ],
            'plans' => [
                [
                    'name' => 'الباقة الأساسية',
                    'price' => BASIC_PLAN_PRICE . ' ₪',
                    'features' => [
                        'حتى 100 مريض',
                        'إدارة المواعيد',
                        'الوصفات الإلكترونية',
                        'التقارير الأساسية',
                        'دعم فني'
                    ]
                ],
                [
                    'name' => 'الباقة المتقدمة',
                    'price' => PRO_PLAN_PRICE . ' ₪',
                    'features' => [
                        'مرضى غير محدودين',
                        'جميع ميزات الباقة الأساسية',
                        'صفحة هبوط مخصصة',
                        'تقارير متقدمة',
                        'رفع الملفات',
                        'دعم فني أولوية'
                    ],
                    'popular' => true
                ],
                [
                    'name' => 'باقة المؤسسات',
                    'price' => ENTERPRISE_PLAN_PRICE . ' ₪',
                    'features' => [
                        'جميع ميزات الباقة المتقدمة',
                        'عيادات متعددة',
                        'مستخدمين متعددين',
                        'تخصيص كامل',
                        'تدريب مخصص',
                        'دعم فني 24/7'
                    ]
                ]
            ]
        ];
        
        $this->view->renderWithLayout('home/index', 'public', $data);
    }
    
    /**
     * صفحة حول النظام
     * About page
     */
    public function about() {
        $data = [
            'title' => 'حول نظام حكيم',
            'description' => 'تعرف على نظام حكيم وكيف يمكنه مساعدة عيادتك'
        ];
        
        $this->view->renderWithLayout('pages/about', 'public', $data);
    }
    
    /**
     * صفحة الميزات
     * Features page
     */
    public function features() {
        $data = [
            'title' => 'ميزات نظام حكيم',
            'description' => 'اكتشف جميع الميزات المتقدمة التي يوفرها نظام حكيم'
        ];
        
        $this->view->renderWithLayout('pages/features', 'public', $data);
    }
    
    /**
     * صفحة الأسعار
     * Pricing page
     */
    public function pricing() {
        $data = [
            'title' => 'أسعار الاشتراك',
            'description' => 'اختر الباقة المناسبة لعيادتك',
            'plans' => [
                [
                    'name' => 'الباقة الأساسية',
                    'price' => BASIC_PLAN_PRICE,
                    'period' => 'شهرياً',
                    'currency' => '₪',
                    'description' => 'مثالية للعيادات الصغيرة',
                    'features' => [
                        'حتى 100 مريض',
                        'إدارة المواعيد',
                        'الوصفات الإلكترونية',
                        'التقارير الأساسية',
                        'دعم فني عبر البريد',
                        '5 جيجا تخزين'
                    ]
                ],
                [
                    'name' => 'الباقة المتقدمة',
                    'price' => PRO_PLAN_PRICE,
                    'period' => 'شهرياً',
                    'currency' => '₪',
                    'description' => 'الأكثر شعبية للعيادات المتوسطة',
                    'features' => [
                        'مرضى غير محدودين',
                        'جميع ميزات الباقة الأساسية',
                        'صفحة هبوط مخصصة',
                        'تقارير متقدمة وإحصائيات',
                        'رفع الملفات والصور',
                        'إشعارات SMS',
                        'دعم فني أولوية',
                        '50 جيجا تخزين'
                    ],
                    'popular' => true
                ],
                [
                    'name' => 'باقة المؤسسات',
                    'price' => ENTERPRISE_PLAN_PRICE,
                    'period' => 'شهرياً',
                    'currency' => '₪',
                    'description' => 'للمراكز الطبية الكبيرة',
                    'features' => [
                        'جميع ميزات الباقة المتقدمة',
                        'عيادات متعددة',
                        'مستخدمين متعددين',
                        'تخصيص كامل للواجهة',
                        'API متقدم',
                        'تدريب مخصص للفريق',
                        'دعم فني 24/7',
                        'تخزين غير محدود'
                    ]
                ]
            ]
        ];
        
        $this->view->renderWithLayout('pages/pricing', 'public', $data);
    }
    
    /**
     * صفحة التواصل
     * Contact page
     */
    public function contact() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleContactForm();
        }
        
        $data = [
            'title' => 'تواصل معنا',
            'description' => 'نحن هنا لمساعدتك، تواصل معنا في أي وقت'
        ];
        
        $this->view->renderWithLayout('pages/contact', 'public', $data);
    }
    
    /**
     * معالجة نموذج التواصل
     * Handle contact form
     */
    private function handleContactForm() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'name' => 'required|min:2',
            'email' => 'required|email',
            'phone' => 'required|phone',
            'message' => 'required|min:10'
        ]);
        
        if (empty($errors)) {
            try {
                // حفظ الرسالة في قاعدة البيانات
                $contactModel = $this->model('ContactMessage');
                $messageData = [
                    'name' => $data['name'],
                    'email' => $data['email'],
                    'phone' => $data['phone'],
                    'subject' => $data['subject'] ?? 'رسالة من صفحة التواصل',
                    'message' => $data['message'],
                    'status' => 'new'
                ];
                
                $messageId = $contactModel->createMessage($messageData);
                
                if ($messageId) {
                    $this->notify('تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.', 'success');
                } else {
                    $this->notify('حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.', 'error');
                }
            } catch (Exception $e) {
                $this->notify('حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.', 'error');
            }
            
            $this->redirect('contact');
        } else {
            $this->notify('يرجى تصحيح الأخطاء في النموذج.', 'error');
        }
    }
    
    /**
     * صفحة تسجيل عيادة جديدة
     * Clinic registration page
     */
    public function register() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleClinicRegistration();
        }
        
        $data = [
            'title' => 'تسجيل عيادة جديدة',
            'description' => 'ابدأ تجربتك المجانية لمدة ' . TRIAL_DAYS . ' يوم'
        ];
        
        $this->redirect('auth/register');
    }
    
    /**
     * معالجة تسجيل العيادة
     * Handle clinic registration
     */
    private function handleClinicRegistration() {
        $data = $this->sanitize($_POST);
        
        $errors = $this->validate($data, [
            'clinic_name' => 'required|min:3',
            'doctor_name' => 'required|min:3',
            'email' => 'required|email',
            'phone' => 'required|phone',
            'specialization' => 'required',
            'password' => 'required|min:6'
        ]);
        
        if (empty($errors)) {
            try {
                $this->db->beginTransaction();
                
                // إنشاء العيادة
                $clinicModel = $this->model('Clinic');
                $clinicId = $clinicModel->createClinic([
                    'name' => $data['clinic_name'],
                    'description' => $data['description'] ?? '',
                    'phone' => $data['phone'],
                    'email' => $data['email'],
                    'specialization' => $data['specialization'],
                    'address' => $data['address'] ?? ''
                ]);
                
                if ($clinicId) {
                    // إنشاء المستخدم الطبيب
                    $userModel = $this->model('User');
                    $userId = $userModel->createUser([
                        'full_name' => $data['doctor_name'],
                        'email' => $data['email'],
                        'phone' => $data['phone'],
                        'password' => $data['password'],
                        'role' => 'doctor',
                        'clinic_id' => $clinicId
                    ]);
                    
                    if ($userId) {
                        $this->db->commit();
                        $this->notify('تم تسجيل العيادة بنجاح! يمكنك الآن تسجيل الدخول.', 'success');
                        $this->redirect('login');
                    } else {
                        throw new Exception('فشل في إنشاء المستخدم');
                    }
                } else {
                    throw new Exception('فشل في إنشاء العيادة');
                }
                
            } catch (Exception $e) {
                $this->db->rollback();
                $this->notify('حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.', 'error');
            }
        } else {
            $this->notify('يرجى تصحيح الأخطاء في النموذج.', 'error');
        }
    }

    /**
     * صفحة مركز المساعدة
     */
    public function help() {
        $data = [
            'title' => 'مركز المساعدة - نظام حكيم'
        ];
        $this->view->renderWithLayout('pages/help', 'public', $data);
    }

    /**
     * صفحة الأسئلة الشائعة
     */
    public function faq() {
        $data = [
            'title' => 'الأسئلة الشائعة - نظام حكيم'
        ];
        $this->view->renderWithLayout('pages/faq', 'public', $data);
    }

    /**
     * صفحة سياسة الخصوصية
     */
    public function privacy() {
        $data = [
            'title' => 'سياسة الخصوصية - نظام حكيم'
        ];
        $this->view->renderWithLayout('pages/privacy', 'public', $data);
    }

    /**
     * صفحة شروط الاستخدام
     */
    public function terms() {
        $data = [
            'title' => 'شروط الاستخدام - نظام حكيم'
        ];
        $this->view->renderWithLayout('pages/terms', 'public', $data);
    }

    /**
     * صفحة سياسة الكوكيز
     */
    public function cookies() {
        $data = [
            'title' => 'سياسة الكوكيز - نظام حكيم'
        ];
        $this->view->renderWithLayout('pages/cookies', 'public', $data);
    }

    /**
     * صفحة العرض التجريبي
     */
    public function demo() {
        $data = [
            'title' => 'عرض تجريبي - نظام حكيم'
        ];
        $this->view->renderWithLayout('pages/demo', 'public', $data);
    }

    /**
     * صفحة الوثائق
     */
    public function docs() {
        $data = [
            'title' => 'الوثائق - نظام حكيم'
        ];
        $this->view->renderWithLayout('pages/docs', 'public', $data);
    }

    /**
     * صفحة الدروس التعليمية
     */
    public function tutorials() {
        $data = [
            'title' => 'دروس تعليمية - نظام حكيم'
        ];
        $this->view->renderWithLayout('pages/tutorials', 'public', $data);
    }

    /**
     * صفحة الدعم الفني
     */
    public function support() {
        $data = [
            'title' => 'الدعم الفني - نظام حكيم'
        ];
        $this->view->renderWithLayout('pages/support', 'public', $data);
    }
}
?>
