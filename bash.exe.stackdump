Stack trace:
Frame         Function      Args
0007FFFF9890  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9890, 0007FFFF8790) msys-2.0.dll+0x1FE8E
0007FFFF9890  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9B68) msys-2.0.dll+0x67F9
0007FFFF9890  000210046832 (000210286019, 0007FFFF9748, 0007FFFF9890, 000000000000) msys-2.0.dll+0x6832
0007FFFF9890  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9890  000210068E24 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9B70  00021006A225 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCEC940000 ntdll.dll
7FFCEB490000 KERNEL32.DLL
7FFCE9A90000 KERNELBASE.dll
7FFCEB610000 USER32.dll
7FFCE9F10000 win32u.dll
7FFCEC390000 GDI32.dll
7FFCEA5A0000 gdi32full.dll
7FFCEA370000 msvcp_win.dll
7FFCEA220000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCEA840000 advapi32.dll
7FFCEC850000 msvcrt.dll
7FFCEC3C0000 sechost.dll
7FFCEA6E0000 RPCRT4.dll
7FFCE8FA0000 CRYPTBASE.DLL
7FFCEA000000 bcryptPrimitives.dll
7FFCEC810000 IMM32.DLL
