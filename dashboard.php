<?php
/**
 * لوحة التحكم الرئيسية
 * Main Dashboard
 */

// تشغيل الجلسات
session_start();

// تعريف المسارات
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('VIEWS_PATH', APP_PATH . '/views');

// تحميل الإعدادات
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/core/AuthGuard.php';
requireAuth();
require_once APP_PATH . '/controllers/DashboardController.php';

// إعادة توجيه الأدمن إلى لوحة تحكم الأدمن
if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin') {
    header('Location: admin/dashboard.php');
    exit;
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// تشغيل لوحة التحكم
$controller = new DashboardController();
$controller->index();
?>
