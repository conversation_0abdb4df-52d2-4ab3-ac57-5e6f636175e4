<?php
/**
 * حكيم - نظام إدارة العيادات الطبية
 * Hakim - Medical Clinic Management System
 * 
 * نقطة الدخول الرئيسية للتطبيق
 * Main entry point for the application
 */

// تشغيل الجلسات
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعريف المسارات الأساسية
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('VIEWS_PATH', APP_PATH . '/views');

// تضمين ملف AuthGuard.php
require_once APP_PATH . '/core/AuthGuard.php';
redirectIfAuthenticated();

// التحقق من وجود ملف الإعدادات
if (!file_exists(CONFIG_PATH . '/config.php')) {
    // إعادة توجيه لصفحة التثبيت
    header('Location: install.php');
    exit;
}

// تضمين ملف الإعدادات
require_once CONFIG_PATH . '/config.php';

// التحقق من وجود ملف قاعدة البيانات
if (file_exists(CONFIG_PATH . '/database.php')) {
    require_once CONFIG_PATH . '/database.php';
}

// تضمين الكلاسات الأساسية
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Controller.php';

// نظام توجيه بسيط
$url = $_GET['url'] ?? '';
$url = rtrim($url, '/');

// تحديد الكونترولر والطريقة
$controller = 'Home';
$method = 'index';
$params = [];

if (!empty($url)) {
    $urlParts = explode('/', $url);

    // إذا كان المسار يبدأ بـ home
    if ($urlParts[0] === 'home') {
        $controller = 'Home';
        $method = isset($urlParts[1]) ? $urlParts[1] : 'index';
    }
    // إذا كان المسار يبدأ بـ auth
    elseif ($urlParts[0] === 'auth') {
        $controller = 'Auth';
        $method = isset($urlParts[1]) ? $urlParts[1] : 'login';
    }
    // مسارات أخرى
    else {
        $controller = ucfirst($urlParts[0]);
        $method = isset($urlParts[1]) ? $urlParts[1] : 'index';
        $params = array_slice($urlParts, 2);
    }
}

// تحميل الكونترولر
$controllerFile = APP_PATH . '/controllers/' . $controller . 'Controller.php';

if (file_exists($controllerFile)) {
    require_once $controllerFile;
    $controllerClass = $controller . 'Controller';

    if (class_exists($controllerClass)) {
        $controllerInstance = new $controllerClass();

        if (method_exists($controllerInstance, $method)) {
            try {
                call_user_func_array([$controllerInstance, $method], $params);
            } catch (Exception $e) {
                http_response_code(500);
                echo "خطأ في تشغيل الصفحة: " . $e->getMessage();
                echo "<br>الملف: " . $e->getFile();
                echo "<br>السطر: " . $e->getLine();
                echo "<br>التفاصيل: <pre>" . $e->getTraceAsString() . "</pre>";
            }
        } else {
            // الطريقة غير موجودة
            http_response_code(404);
            echo "الطريقة '{$method}' غير موجودة في الكونترولر '{$controllerClass}'";
            echo "<br>الطرق المتاحة: " . implode(', ', get_class_methods($controllerInstance));
        }
    } else {
        // الكلاس غير موجود
        http_response_code(404);
        echo "الكونترولر غير موجود";
    }
} else {
    // الملف غير موجود
    http_response_code(404);
    echo "الصفحة غير موجودة";
}
?>
