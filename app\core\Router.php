<?php
/**
 * كلاس الراوتر - توجيه الطلبات
 * Router class - Request routing
 */

class Router {
    private $routes = [];
    private $currentController = 'Home';
    private $currentMethod = 'index';
    private $params = [];
    
    public function __construct() {
        $this->setupRoutes();
    }
    
    private function setupRoutes() {
        // الصفحة الرئيسية
        $this->addRoute('', 'Home', 'index');
        $this->addRoute('home', 'Home', 'index');

        // صفحات الموقع العامة
        $this->addRoute('home/features', 'Home', 'features');
        $this->addRoute('home/pricing', 'Home', 'pricing');
        $this->addRoute('home/about', 'Home', 'about');
        $this->addRoute('home/contact', 'Home', 'contact');
        $this->addRoute('home/help', 'Home', 'help');
        $this->addRoute('home/faq', 'Home', 'faq');
        $this->addRoute('home/privacy', 'Home', 'privacy');
        $this->addRoute('home/terms', 'Home', 'terms');
        $this->addRoute('home/cookies', 'Home', 'cookies');
        $this->addRoute('home/demo', 'Home', 'demo');
        $this->addRoute('home/docs', 'Home', 'docs');
        $this->addRoute('home/tutorials', 'Home', 'tutorials');
        $this->addRoute('home/support', 'Home', 'support');

        // المصادقة
        $this->addRoute('auth/login', 'Auth', 'login');
        $this->addRoute('auth/logout', 'Auth', 'logout');
        $this->addRoute('auth/register', 'Auth', 'register');
        $this->addRoute('auth/forgot-password', 'Auth', 'forgotPassword');

        // صفحات العيادات
        $this->addRoute('clinic/([a-zA-Z0-9_-]+)', 'Clinic', 'landing');
        $this->addRoute('book/([a-zA-Z0-9_-]+)', 'Booking', 'create');

        // لوحة التحكم
        $this->addRoute('dashboard', 'Dashboard', 'index');

        // الروابط القديمة للتوافق
        $this->addRoute('login', 'Auth', 'login');
        $this->addRoute('logout', 'Auth', 'logout');
        $this->addRoute('register', 'Auth', 'register');
        
        // إدارة المرضى
        $this->addRoute('patients', 'Patients', 'index');
        $this->addRoute('patients/add', 'Patients', 'add');
        $this->addRoute('patients/edit/([0-9]+)', 'Patients', 'edit');
        $this->addRoute('patients/view/([0-9]+)', 'Patients', 'view');
        
        // إدارة المواعيد
        $this->addRoute('appointments', 'Appointments', 'index');
        $this->addRoute('appointments/calendar', 'Appointments', 'calendar');
        $this->addRoute('appointments/confirm/([0-9]+)', 'Appointments', 'confirm');
        
        // الوصفات
        $this->addRoute('prescriptions', 'Prescriptions', 'index');
        $this->addRoute('prescriptions/add', 'Prescriptions', 'add');
        $this->addRoute('prescriptions/print/([0-9]+)', 'Prescriptions', 'print');
        
        // الفواتير
        $this->addRoute('invoices', 'Invoices', 'index');
        $this->addRoute('invoices/create', 'Invoices', 'create');
        $this->addRoute('invoices/print/([0-9]+)', 'Invoices', 'print');
        
        // التقارير
        $this->addRoute('reports', 'Reports', 'index');
        $this->addRoute('reports/income', 'Reports', 'income');
        $this->addRoute('reports/patients', 'Reports', 'patients');
        
        // إدارة العيادات (للمدير العام)
        $this->addRoute('admin/clinics', 'Admin', 'clinics');
        $this->addRoute('admin/subscriptions', 'Admin', 'subscriptions');
        
        // API endpoints
        $this->addRoute('api/book', 'Api', 'book');
        $this->addRoute('api/appointments', 'Api', 'appointments');
    }
    
    private function addRoute($pattern, $controller, $method) {
        $this->routes[$pattern] = [
            'controller' => $controller,
            'method' => $method
        ];
    }
    
    public function run() {
        try {
            $url = $this->getUrl();

            // البحث عن المسار المطابق
            $routeFound = false;
            foreach ($this->routes as $pattern => $route) {
                if (preg_match('#^' . $pattern . '$#', $url, $matches)) {
                    $this->currentController = $route['controller'];
                    $this->currentMethod = $route['method'];

                    // إزالة المطابقة الكاملة والاحتفاظ بالمعاملات
                    array_shift($matches);
                    $this->params = $matches;
                    $routeFound = true;
                    break;
                }
            }

            // تحميل الكونترولر
            $this->loadController();

        } catch (Exception $e) {
            $this->show404();
        }
    }
    
    private function getUrl() {
        $url = $_GET['url'] ?? '';
        return rtrim($url, '/');
    }
    
    private function loadController() {
        $controllerFile = APP_PATH . '/controllers/' . $this->currentController . 'Controller.php';

        if (file_exists($controllerFile)) {
            require_once $controllerFile;

            $controllerClass = $this->currentController . 'Controller';

            if (class_exists($controllerClass)) {
                $controller = new $controllerClass();

                if (method_exists($controller, $this->currentMethod)) {
                    call_user_func_array([$controller, $this->currentMethod], $this->params);
                } else {
                    $this->show404();
                }
            } else {
                $this->show404();
            }
        } else {
            $this->show404();
        }
    }
    
    private function show404() {
        http_response_code(404);

        // التحقق من وجود صفحة 404 مخصصة
        $errorFile = VIEWS_PATH . '/errors/404.php';
        if (file_exists($errorFile)) {
            require_once $errorFile;
        } else {
            // صفحة 404 بسيطة
            echo "<!DOCTYPE html>";
            echo "<html lang='ar' dir='rtl'>";
            echo "<head><meta charset='UTF-8'><title>الصفحة غير موجودة</title></head>";
            echo "<body style='font-family: Arial, sans-serif; text-align: center; padding: 50px;'>";
            echo "<h1>404 - الصفحة غير موجودة</h1>";
            echo "<p>عذراً، الصفحة التي تبحث عنها غير موجودة.</p>";
            echo "<a href='" . APP_URL . "' style='color: #007BFF; text-decoration: none;'>العودة للصفحة الرئيسية</a>";
            echo "</body></html>";
        }
    }
}
?>
