<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left Side - Reset Password Form -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center">
            <div class="w-100" style="max-width: 450px;">
                <div class="text-center mb-4 animate__animated animate__fadeInDown">
                    <div class="mb-3">
                        <i class="fas fa-lock text-primary" style="font-size: 3.5rem;"></i>
                    </div>
                    <h2 class="fw-bold text-primary">إعادة تعيين كلمة المرور</h2>
                    <p class="text-muted">أدخل كلمة المرور الجديدة</p>
                </div>
                
                <div class="card shadow-lg border-0 animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                    <div class="card-body p-4">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger animate__animated animate__shakeX">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?= $this->escape($error) ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" id="resetForm">
                            <?= $this->csrfField() ?>
                            <input type="hidden" name="token" value="<?= $this->escape($token ?? '') ?>">
                            
                            <div class="mb-3">
                                <label for="password" class="form-label fw-bold">كلمة المرور الجديدة</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="fas fa-lock text-muted"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="كلمة مرور قوية" required minlength="6">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                        <i class="fas fa-eye" id="toggleIcon1"></i>
                                    </button>
                                </div>
                                <small class="text-muted">يجب أن تحتوي على 6 أحرف على الأقل</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label fw-bold">تأكيد كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="fas fa-lock text-muted"></i>
                                    </span>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                           placeholder="أعد كتابة كلمة المرور" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                        <i class="fas fa-eye" id="toggleIcon2"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-success btn-lg enhanced-register-btn">
                                    <i class="fas fa-check me-2"></i>
                                    تحديث كلمة المرور
                                    <span class="btn-shine"></span>
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center">
                            <p class="mb-0 text-muted">
                                <a href="login.php" class="text-primary text-decoration-none fw-bold">
                                    العودة لتسجيل الدخول
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Side - Security Info -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center" 
             style="background: linear-gradient(135deg, #28a745, #20c997);">
            <div class="text-center text-white animate__animated animate__fadeInRight">
                <div class="mb-4">
                    <i class="fas fa-shield-check" style="font-size: 4rem; opacity: 0.9;"></i>
                </div>
                <h3 class="mb-4">كلمة مرور قوية</h3>
                
                <div class="row text-center mb-4">
                    <div class="col-12 mb-3">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-check-circle me-2" style="font-size: 1.2rem;"></i>
                            <span>6 أحرف على الأقل</span>
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-check-circle me-2" style="font-size: 1.2rem;"></i>
                            <span>مزيج من الأحرف والأرقام</span>
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-check-circle me-2" style="font-size: 1.2rem;"></i>
                            <span>تشفير عالي الأمان</span>
                        </div>
                    </div>
                </div>
                
                <div style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 2rem;">
                    <h5 class="mb-3">حماية متقدمة</h5>
                    <p class="mb-0" style="font-size: 0.9rem; opacity: 0.9;">
                        كلمة المرور الخاصة بك محمية بتشفير متقدم ولن يتمكن أحد من الوصول إليها
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const toggleIcon = document.getElementById(fieldId === 'password' ? 'toggleIcon1' : 'toggleIcon2');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Password validation
document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    const form = document.getElementById('resetForm');
    
    function validatePasswords() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('كلمات المرور غير متطابقة');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);
    
    // Form submission
    form.addEventListener('submit', function(e) {
        if (password.value.length < 6) {
            e.preventDefault();
            alert('كلمة المرور يجب أن تحتوي على 6 أحرف على الأقل');
            return false;
        }
        
        if (password.value !== confirmPassword.value) {
            e.preventDefault();
            alert('كلمات المرور غير متطابقة');
            return false;
        }
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
        submitBtn.disabled = true;
        
        // Re-enable after 10 seconds as fallback
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 10000);
    });
});
</script>
