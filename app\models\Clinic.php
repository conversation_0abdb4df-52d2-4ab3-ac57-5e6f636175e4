<?php
/**
 * موديل العيادات
 * Clinic Model
 */

class Clinic extends Model {
    protected $table = 'clinics';
    
    /**
     * البحث عن عيادة بالـ slug
     * Find clinic by slug
     */
    public function findBySlug($slug) {
        return $this->findBy('slug', $slug);
    }
    
    /**
     * إنشاء عيادة جديدة
     * Create new clinic
     */
    public function createClinic($data) {
        // إنشاء slug فريد
        if (!isset($data['slug'])) {
            $data['slug'] = $this->generateSlug($data['name']);
        }
        
        // تعيين قيم افتراضية
        $data['subscription_plan'] = $data['subscription_plan'] ?? 'trial';
        $data['subscription_start'] = $data['subscription_start'] ?? date('Y-m-d');
        $data['subscription_end'] = $data['subscription_end'] ?? date('Y-m-d', strtotime('+' . TRIAL_DAYS . ' days'));
        $data['landing_template'] = $data['landing_template'] ?? 'template1';
        
        // تعيين ساعات العمل الافتراضية
        if (!isset($data['working_hours'])) {
            $data['working_hours'] = json_encode([
                'saturday' => ['start' => '09:00', 'end' => '17:00'],
                'sunday' => ['start' => '09:00', 'end' => '17:00'],
                'monday' => ['start' => '09:00', 'end' => '17:00'],
                'tuesday' => ['start' => '09:00', 'end' => '17:00'],
                'wednesday' => ['start' => '09:00', 'end' => '17:00'],
                'thursday' => ['start' => '09:00', 'end' => '17:00'],
                'friday' => 'closed'
            ]);
        }
        
        return $this->create($data);
    }
    
    /**
     * توليد slug فريد
     * Generate unique slug
     */
    private function generateSlug($name) {
        // تحويل الاسم إلى slug
        $slug = strtolower(trim($name));
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        $slug = preg_replace('/[\s-]+/', '-', $slug);
        $slug = trim($slug, '-');
        
        // التحقق من التفرد
        $counter = 1;
        $originalSlug = $slug;
        
        while ($this->findBySlug($slug)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * الحصول على العيادات النشطة
     * Get active clinics
     */
    public function getActiveClinics() {
        return $this->where('is_active', 1);
    }
    
    /**
     * الحصول على العيادات المنتهية الصلاحية
     * Get expired clinics
     */
    public function getExpiredClinics() {
        $sql = "SELECT * FROM {$this->table} 
                WHERE subscription_end < CURDATE() 
                AND is_active = 1 
                ORDER BY subscription_end";
        return $this->query($sql);
    }
    
    /**
     * الحصول على العيادات التي ستنتهي قريباً
     * Get clinics expiring soon
     */
    public function getExpiringSoon($days = 7) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE subscription_end BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
                AND is_active = 1 
                ORDER BY subscription_end";
        return $this->query($sql, [$days]);
    }
    
    /**
     * تحديث خطة الاشتراك
     * Update subscription plan
     */
    public function updateSubscription($clinicId, $plan, $startDate, $endDate) {
        return $this->update($clinicId, [
            'subscription_plan' => $plan,
            'subscription_start' => $startDate,
            'subscription_end' => $endDate
        ]);
    }
    
    /**
     * تحديث ساعات العمل
     * Update working hours
     */
    public function updateWorkingHours($clinicId, $workingHours) {
        return $this->update($clinicId, [
            'working_hours' => json_encode($workingHours)
        ]);
    }
    
    /**
     * تحديث قالب الصفحة
     * Update landing template
     */
    public function updateTemplate($clinicId, $template, $customColors = null) {
        $data = ['landing_template' => $template];
        
        if ($customColors) {
            $data['custom_colors'] = json_encode($customColors);
        }
        
        return $this->update($clinicId, $data);
    }
    
    /**
     * الحصول على إحصائيات العيادة
     * Get clinic statistics
     */
    public function getClinicStats($clinicId) {
        // عدد المرضى
        $patientsCount = $this->queryOne(
            "SELECT COUNT(*) as count FROM patients WHERE clinic_id = ?",
            [$clinicId]
        )['count'];
        
        // عدد المواعيد هذا الشهر
        $appointmentsCount = $this->queryOne(
            "SELECT COUNT(*) as count FROM appointments 
             WHERE clinic_id = ? AND MONTH(appointment_date) = MONTH(CURDATE()) 
             AND YEAR(appointment_date) = YEAR(CURDATE())",
            [$clinicId]
        )['count'];
        
        // إجمالي الدخل هذا الشهر
        $monthlyIncome = $this->queryOne(
            "SELECT COALESCE(SUM(amount), 0) as total FROM sessions 
             WHERE clinic_id = ? AND payment_status = 'paid' 
             AND MONTH(session_date) = MONTH(CURDATE()) 
             AND YEAR(session_date) = YEAR(CURDATE())",
            [$clinicId]
        )['total'];
        
        // المواعيد المعلقة
        $pendingAppointments = $this->queryOne(
            "SELECT COUNT(*) as count FROM appointments 
             WHERE clinic_id = ? AND status = 'pending'",
            [$clinicId]
        )['count'];
        
        return [
            'patients_count' => $patientsCount,
            'appointments_count' => $appointmentsCount,
            'monthly_income' => $monthlyIncome,
            'pending_appointments' => $pendingAppointments
        ];
    }
    
    /**
     * البحث في العيادات
     * Search clinics
     */
    public function searchClinics($term) {
        return $this->search(['name', 'description', 'specialization', 'address'], $term);
    }
    
    /**
     * الحصول على العيادات حسب التخصص
     * Get clinics by specialization
     */
    public function getBySpecialization($specialization) {
        return $this->where('specialization', $specialization);
    }
    
    /**
     * التحقق من انتهاء الاشتراك
     * Check if subscription is expired
     */
    public function isSubscriptionExpired($clinicId) {
        $clinic = $this->find($clinicId);
        if (!$clinic) return true;
        
        return strtotime($clinic['subscription_end']) < time();
    }
    
    /**
     * التحقق من صحة الاشتراك
     * Check if subscription is valid
     */
    public function isSubscriptionValid($clinicId) {
        $clinic = $this->find($clinicId);
        if (!$clinic || !$clinic['is_active']) return false;
        
        return strtotime($clinic['subscription_end']) >= time();
    }
    
    /**
     * الحصول على أيام متبقية في الاشتراك
     * Get remaining days in subscription
     */
    public function getRemainingDays($clinicId) {
        $clinic = $this->find($clinicId);
        if (!$clinic) return 0;
        
        $endDate = strtotime($clinic['subscription_end']);
        $today = time();
        
        if ($endDate < $today) return 0;
        
        return ceil(($endDate - $today) / (24 * 60 * 60));
    }
    
    /**
     * تفعيل/إلغاء تفعيل العيادة
     * Activate/Deactivate clinic
     */
    public function toggleActive($clinicId) {
        $clinic = $this->find($clinicId);
        if ($clinic) {
            $newStatus = $clinic['is_active'] ? 0 : 1;
            return $this->update($clinicId, ['is_active' => $newStatus]);
        }
        return false;
    }

    /**
     * الحصول على إجمالي عدد العيادات
     * Get total number of clinics
     */
    public function getTotalClinics() {
        $result = $this->queryOne("SELECT COUNT(*) as count FROM {$this->table}");
        return $result['count'] ?? 0;
    }

    /**
     * الحصول على عدد الاشتراكات النشطة
     * Get number of active subscriptions
     */
    public function getActiveSubscriptions() {
        $result = $this->queryOne(
            "SELECT COUNT(*) as count FROM {$this->table} 
             WHERE subscription_end >= CURDATE() AND is_active = 1"
        );
        return $result['count'] ?? 0;
    }

    /**
     * الحصول على عدد الاشتراكات المنتهية
     * Get number of expired subscriptions
     */
    public function getExpiredSubscriptions() {
        $result = $this->queryOne(
            "SELECT COUNT(*) as count FROM {$this->table} 
             WHERE subscription_end < CURDATE() AND is_active = 1"
        );
        return $result['count'] ?? 0;
    }

    /**
     * الحصول على إجمالي الإيرادات
     * Get total revenue
     */
    public function getTotalRevenue() {
        $result = $this->queryOne(
            "SELECT COALESCE(SUM(amount), 0) as total FROM sessions 
             WHERE payment_status = 'paid'"
        );
        return $result['total'] ?? 0;
    }

    /**
     * الحصول على إحصائيات الاشتراكات
     * Get subscription statistics
     */
    public function getSubscriptionStats() {
        $stats = [];
        
        // الاشتراكات حسب الخطة
        $plans = $this->query(
            "SELECT subscription_plan, COUNT(*) as count 
             FROM {$this->table} 
             WHERE is_active = 1 
             GROUP BY subscription_plan"
        );
        
        foreach ($plans as $plan) {
            $stats[$plan['subscription_plan']] = $plan['count'];
        }
        
        return $stats;
    }

    /**
     * الحصول على الإيرادات الشهرية
     * Get monthly revenue
     */
    public function getMonthlyRevenue() {
        $result = $this->queryOne(
            "SELECT COALESCE(SUM(amount), 0) as total FROM sessions 
             WHERE payment_status = 'paid' 
             AND MONTH(session_date) = MONTH(CURDATE()) 
             AND YEAR(session_date) = YEAR(CURDATE())"
        );
        return $result['total'] ?? 0;
    }

    /**
     * الحصول على العيادات مع تفاصيل الاشتراك
     * Get clinics with subscription details
     */
    public function getClinicsWithSubscriptions() {
        $clinics = $this->query(
            "SELECT c.id, c.name as clinic_name, c.email, c.phone, c.subscription_plan,
                    c.subscription_start, c.subscription_end, c.is_active,
                    DATEDIFF(c.subscription_end, CURDATE()) as days_remaining,
                    u.full_name as manager_name
             FROM {$this->table} c
             LEFT JOIN users u ON c.id = u.clinic_id AND u.role = 'doctor'
             ORDER BY c.subscription_end ASC"
        );
        
        // إضافة حالة الاشتراك المحسوبة والقيم الافتراضية
        foreach ($clinics as &$clinic) {
            $daysRemaining = $clinic['days_remaining'];
            
            if ($daysRemaining < 0) {
                $clinic['subscription_status'] = 'expired';
            } elseif ($daysRemaining <= 7) {
                $clinic['subscription_status'] = 'expiring';
            } else {
                $clinic['subscription_status'] = 'active';
            }
            
            // إضافة قيم افتراضية للحقول المفقودة
            $clinic['manager_name'] = $clinic['manager_name'] ?? 'غير محدد';
            $clinic['subscription_end_date'] = $clinic['subscription_end'];
        }
        
        return $clinics;
    }
}
?>
