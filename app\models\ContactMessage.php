<?php
/**
 * موديل رسائل التواصل
 * Contact Message Model
 */

class ContactMessage extends Model {
    protected $table = 'contact_messages';
    
    /**
     * إنشاء رسالة جديدة
     * Create new contact message
     */
    public function createMessage($data) {
        return $this->create($data);
    }
    
    /**
     * الحصول على الرسائل الجديدة
     * Get new messages
     */
    public function getNewMessages() {
        return $this->where('status', 'new');
    }
    
    /**
     * الحصول على الرسائل المقروءة
     * Get read messages
     */
    public function getReadMessages() {
        return $this->where('status', 'read');
    }
    
    /**
     * الحصول على الرسائل المجاب عليها
     * Get replied messages
     */
    public function getRepliedMessages() {
        return $this->where('status', 'replied');
    }
    
    /**
     * تحديث حالة الرسالة
     * Update message status
     */
    public function updateStatus($messageId, $status) {
        return $this->update($messageId, ['status' => $status]);
    }
    
    /**
     * إضافة ملاحظات الأدمن
     * Add admin notes
     */
    public function addAdminNotes($messageId, $notes) {
        return $this->update($messageId, ['admin_notes' => $notes]);
    }
    
    /**
     * الحصول على عدد الرسائل الجديدة
     * Get count of new messages
     */
    public function getNewMessagesCount() {
        $result = $this->queryOne("SELECT COUNT(*) as count FROM {$this->table} WHERE status = 'new'");
        return $result['count'] ?? 0;
    }
    
    /**
     * الحصول على الرسائل مع الترتيب حسب التاريخ
     * Get messages ordered by date
     */
    public function getMessagesOrdered($limit = 10) {
        $limit = (int) $limit;
        if ($limit <= 0) $limit = 10;
        $sql = "SELECT * FROM {$this->table} ORDER BY created_at DESC LIMIT $limit";
        return $this->query($sql);
    }
    
    /**
     * البحث في الرسائل
     * Search messages
     */
    public function searchMessages($term) {
        return $this->search(['name', 'email', 'subject', 'message'], $term);
    }
    
    /**
     * الحصول على إحصائيات الرسائل
     * Get message statistics
     */
    public function getMessageStats() {
        $stats = [];
        
        // الرسائل حسب الحالة
        $statuses = $this->query(
            "SELECT status, COUNT(*) as count FROM {$this->table} GROUP BY status"
        );
        
        foreach ($statuses as $status) {
            $stats[$status['status']] = $status['count'];
        }
        
        // إجمالي الرسائل
        $total = $this->queryOne("SELECT COUNT(*) as count FROM {$this->table}");
        $stats['total'] = $total['count'] ?? 0;
        
        return $stats;
    }

    /**
     * الحصول على الرسائل الحديثة للأدمن
     * Get recent messages for admin
     */
    public function getRecentMessages($limit = 5) {
        $limit = (int) $limit;
        if ($limit <= 0) $limit = 5;
        $sql = "SELECT * FROM {$this->table} ORDER BY created_at DESC LIMIT $limit";
        return $this->query($sql);
    }

    /**
     * تحديث حالة الرسالة إلى مقروءة
     * Mark message as read
     */
    public function markAsRead($messageId) {
        return $this->update($messageId, [
            'status' => 'read',
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * حذف رسالة
     * Delete message
     */
    public function deleteMessage($messageId) {
        return $this->delete($messageId);
    }
}
?>