<?php
/**
 * إضافة وصفة طبية جديدة
 * Add New Prescription
 */
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-plus-circle"></i>
                        إضافة وصفة طبية جديدة
                    </h4>
                    <a href="prescriptions.php" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i>
                        العودة للوصفات
                    </a>
                </div>
                <div class="card-body">
                    <form method="POST" action="prescriptions.php?action=add">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="patient_id">المريض <span class="text-danger">*</span></label>
                                    <select name="patient_id" id="patient_id" class="form-control" required>
                                        <option value="">اختر المريض</option>
                                        <?php foreach ($patients as $patient): ?>
                                            <option value="<?= $patient['id'] ?>">
                                                <?= htmlspecialchars($patient['full_name']) ?> - <?= $patient['phone'] ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="session_id">الجلسة الطبية</label>
                                    <select name="session_id" id="session_id" class="form-control">
                                        <option value="">اختر الجلسة (اختياري)</option>
                                        <?php foreach ($sessions as $session): ?>
                                            <option value="<?= $session['id'] ?>">
                                                <?= htmlspecialchars($session['patient_name']) ?> - 
                                                <?= date('Y-m-d', strtotime($session['session_date'])) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="prescription_date">تاريخ الوصفة <span class="text-danger">*</span></label>
                                    <input type="date" name="prescription_date" id="prescription_date" class="form-control" 
                                           value="<?= date('Y-m-d') ?>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="prescription_number">رقم الوصفة</label>
                                    <input type="text" name="prescription_number" id="prescription_number" class="form-control" 
                                           value="<?= $prescriptionNumber ?>" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="diagnosis">التشخيص</label>
                            <textarea name="diagnosis" id="diagnosis" class="form-control" rows="3" 
                                      placeholder="أدخل التشخيص الطبي..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="medications">الأدوية <span class="text-danger">*</span></label>
                            <textarea name="medications" id="medications" class="form-control" rows="5" 
                                      placeholder="أدخل قائمة الأدوية الموصوفة..." required></textarea>
                            <small class="form-text text-muted">
                                مثال: باراسيتامول 500 مجم - قرص واحد كل 6 ساعات
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="dosage_instructions">تعليمات الجرعة</label>
                            <textarea name="dosage_instructions" id="dosage_instructions" class="form-control" rows="3" 
                                      placeholder="أدخل تعليمات الجرعة..."></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="duration">مدة العلاج</label>
                                    <input type="text" name="duration" id="duration" class="form-control" 
                                           placeholder="مثال: 7 أيام">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">حالة الوصفة</label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="active">نشطة</option>
                                        <option value="completed">مكتملة</option>
                                        <option value="cancelled">ملغية</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="instructions">تعليمات إضافية</label>
                            <textarea name="instructions" id="instructions" class="form-control" rows="3" 
                                      placeholder="أدخل أي تعليمات إضافية للمريض..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات</label>
                            <textarea name="notes" id="notes" class="form-control" rows="3" 
                                      placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ الوصفة
                            </button>
                            <a href="prescriptions.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الجلسات عند اختيار المريض
    document.getElementById('patient_id').addEventListener('change', function() {
        const patientId = this.value;
        const sessionSelect = document.getElementById('session_id');
        
        // إعادة تعيين قائمة الجلسات
        sessionSelect.innerHTML = '<option value="">اختر الجلسة (اختياري)</option>';
        
        if (patientId) {
            // يمكن إضافة AJAX هنا لجلب جلسات المريض المحدد
            // للآن سنعرض جميع الجلسات
        }
    });
});
</script> 