<?php
/**
 * موديل المستخدمين
 * User Model
 */

class User extends Model {
    protected $table = 'users';
    
    /**
     * البحث عن مستخدم بالبريد الإلكتروني
     * Find user by email
     */
    public function findByEmail($email) {
        return $this->findBy('email', $email);
    }
    
    /**
     * البحث عن مستخدم باسم المستخدم
     * Find user by username
     */
    public function findByUsername($username) {
        return $this->findBy('username', $username);
    }
    
    /**
     * التحقق من كلمة المرور
     * Verify password
     */
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * تشفير كلمة المرور
     * Hash password
     */
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * إنشاء مستخدم جديد
     * Create new user
     */
    public function createUser($data) {
        // تشفير كلمة المرور
        if (isset($data['password'])) {
            $data['password'] = $this->hashPassword($data['password']);
        }
        
        // إنشاء اسم مستخدم فريد إذا لم يتم توفيره
        if (!isset($data['username'])) {
            $data['username'] = $this->generateUsername($data['full_name']);
        }
        
        return $this->create($data);
    }
    
    /**
     * توليد اسم مستخدم فريد
     * Generate unique username
     */
    private function generateUsername($fullName) {
        // تحويل الاسم إلى اسم مستخدم
        $username = strtolower(str_replace(' ', '.', $fullName));
        $username = preg_replace('/[^a-z0-9.]/', '', $username);
        
        // التحقق من التفرد
        $counter = 1;
        $originalUsername = $username;
        
        while ($this->findByUsername($username)) {
            $username = $originalUsername . $counter;
            $counter++;
        }
        
        return $username;
    }
    
    /**
     * الحصول على مستخدمي عيادة معينة
     * Get users of specific clinic
     */
    public function getClinicUsers($clinicId) {
        $sql = "SELECT * FROM {$this->table} WHERE clinic_id = ? AND is_active = 1 ORDER BY role, full_name";
        return $this->query($sql, [$clinicId]);
    }
    
    /**
     * الحصول على الأطباء
     * Get doctors
     */
    public function getDoctors($clinicId = null) {
        $sql = "SELECT * FROM {$this->table} WHERE role = 'doctor' AND is_active = 1";
        $params = [];
        
        if ($clinicId) {
            $sql .= " AND clinic_id = ?";
            $params[] = $clinicId;
        }
        
        $sql .= " ORDER BY full_name";
        return $this->query($sql, $params);
    }
    
    /**
     * تحديث آخر تسجيل دخول
     * Update last login
     */
    public function updateLastLogin($userId) {
        $sql = "UPDATE {$this->table} SET last_login = NOW() WHERE id = ?";
        return $this->db->query($sql, [$userId]);
    }
    
    /**
     * تغيير كلمة المرور
     * Change password
     */
    public function changePassword($userId, $newPassword) {
        $hashedPassword = $this->hashPassword($newPassword);
        return $this->update($userId, ['password' => $hashedPassword]);
    }
    
    /**
     * تحديث الملف الشخصي
     * Update profile
     */
    public function updateProfile($userId, $data) {
        // إزالة كلمة المرور من البيانات إذا كانت فارغة
        if (isset($data['password']) && empty($data['password'])) {
            unset($data['password']);
        } else if (isset($data['password'])) {
            $data['password'] = $this->hashPassword($data['password']);
        }
        
        return $this->update($userId, $data);
    }
    
    /**
     * تفعيل/إلغاء تفعيل المستخدم
     * Activate/Deactivate user
     */
    public function toggleActive($userId) {
        $user = $this->find($userId);
        if ($user) {
            $newStatus = $user['is_active'] ? 0 : 1;
            return $this->update($userId, ['is_active' => $newStatus]);
        }
        return false;
    }
    
    /**
     * البحث في المستخدمين
     * Search users
     */
    public function searchUsers($term, $clinicId = null) {
        $sql = "SELECT * FROM {$this->table} WHERE (full_name LIKE ? OR email LIKE ? OR username LIKE ?)";
        $params = ["%{$term}%", "%{$term}%", "%{$term}%"];
        
        if ($clinicId) {
            $sql .= " AND clinic_id = ?";
            $params[] = $clinicId;
        }
        
        $sql .= " ORDER BY full_name";
        return $this->query($sql, $params);
    }
    
    /**
     * الحصول على إحصائيات المستخدمين
     * Get user statistics
     */
    public function getUserStats($clinicId = null) {
        $sql = "SELECT 
                    role,
                    COUNT(*) as count,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_count
                FROM {$this->table}";
        $params = [];
        
        if ($clinicId) {
            $sql .= " WHERE clinic_id = ?";
            $params[] = $clinicId;
        }
        
        $sql .= " GROUP BY role";
        return $this->query($sql, $params);
    }
    
    /**
     * التحقق من وجود البريد الإلكتروني
     * Check if email exists
     */
    public function emailExists($email, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE email = ?";
        $params = [$email];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->queryOne($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * التحقق من وجود اسم المستخدم
     * Check if username exists
     */
    public function usernameExists($username, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE username = ?";
        $params = [$username];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->queryOne($sql, $params);
        return $result['count'] > 0;
    }

    /**
     * الحصول على إجمالي عدد المستخدمين
     * Get total number of users
     */
    public function getTotalUsers() {
        $result = $this->queryOne("SELECT COUNT(*) as count FROM {$this->table} WHERE is_active = 1");
        return $result['count'] ?? 0;
    }

    /**
     * الحصول على المستخدمين حسب الدور
     * Get users by role
     */
    public function getUsersByRole($role) {
        return $this->where('role', $role);
    }

    /**
     * الحصول على جميع المستخدمين
     * Get all users
     */
    public function getAllUsers() {
        $sql = "SELECT u.*, c.name as clinic_name 
                FROM {$this->table} u 
                LEFT JOIN clinics c ON u.clinic_id = c.id 
                ORDER BY u.role, u.full_name";
        return $this->query($sql);
    }

    /**
     * الحصول على إحصائيات مستخدم واحد
     * Get single user statistics
     */
    public function getSingleUserStats($userId) {
        $stats = [
            'total_logins' => 0,
            'last_activity' => null,
            'created_at' => null,
            'is_active' => 0
        ];

        // جلب بيانات المستخدم الأساسية
        $user = $this->find($userId);
        if ($user) {
            $stats['created_at'] = $user['created_at'];
            $stats['is_active'] = $user['is_active'];
            $stats['last_activity'] = $user['last_login'];
        }

        // يمكن إضافة المزيد من الإحصائيات هنا حسب الحاجة
        // مثل عدد مرات تسجيل الدخول، النشاطات الأخيرة، إلخ

        return $stats;
    }
}
?>
