<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $data['title'] ?? 'المستخدمين - لوحة تحكم الأدمن' ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../public/favicon.svg">
    <link rel="icon" type="image/png" href="../public/favicon-32x32.png" sizes="32x32">
    <link rel="apple-touch-icon" href="../public/favicon.svg">

    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #007BFF;
            --primary-sky-blue: #87CEEB;
            --primary-white: #FFFFFF;
            --primary-black: #333333;
            --light-gray: #F8F9FA;
            --border-color: #E0E0E0;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-hover: 0 4px 20px rgba(0,0,0,0.15);
            --admin-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--light-gray);
            color: var(--primary-black);
        }

        .sidebar {
            min-height: 100vh;
            background: var(--admin-gradient);
            color: var(--primary-white);
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            overflow-y: auto;
            max-height: 100vh;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 15px 20px;
            border-radius: 10px;
            margin: 5px 15px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid transparent;
            text-decoration: none;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.15);
            color: var(--primary-white);
            transform: translateX(-3px);
            border-color: rgba(255,255,255,0.2);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-white);
            color: var(--primary-blue);
            font-weight: 600;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .main-content {
            margin-right: 250px;
            padding: 25px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: var(--shadow);
            margin-bottom: 25px;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-2px);
        }

        .card-header {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border-radius: 0 !important;
            border: none;
            padding: 20px;
            font-weight: 600;
        }

        .admin-header {
            background: var(--admin-gradient);
            color: var(--primary-white);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .table th {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border: none;
            font-weight: 600;
            padding: 15px;
        }

        .table td {
            padding: 15px;
            border-color: var(--border-color);
            vertical-align: middle;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
        }

        .badge {
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.8rem;
        }

        .btn-group .btn {
            margin: 0 2px;
            border-radius: 8px;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <h4 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    لوحة تحكم الأدمن
                </h4>
                <small class="text-white-50">نظام إدارة العيادات</small>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="dashboard.php" data-page="dashboard">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    الرئيسية
                </a>
                <a class="nav-link" href="clinics.php" data-page="clinics">
                    <i class="fas fa-hospital me-2"></i>
                    العيادات
                </a>
                <a class="nav-link" href="subscriptions.php" data-page="subscriptions">
                    <i class="fas fa-credit-card me-2"></i>
                    الاشتراكات
                </a>
                <a class="nav-link" href="users.php" data-page="users">
                    <i class="fas fa-users me-2"></i>
                    المستخدمين
                </a>
                <a class="nav-link" href="revenue.php" data-page="revenue">
                    <i class="fas fa-chart-line me-2"></i>
                    الإيرادات
                </a>
                <a class="nav-link" href="messages.php" data-page="messages">
                    <i class="fas fa-envelope me-2"></i>
                    الرسائل
                </a>
                <a class="nav-link" href="settings.php" data-page="settings">
                    <i class="fas fa-cog me-2"></i>
                    الإعدادات
                </a>
                <hr class="text-white-50">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    تسجيل الخروج
                </a>
            </nav>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="admin-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="fas fa-users me-3"></i>
                        إدارة المستخدمين
                    </h1>
                    <p class="mb-0 mt-2">عرض وإدارة جميع المستخدمين في النظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-light me-2" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button class="btn btn-light" onclick="addNewUser()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مستخدم جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-primary"><?= count($data['users']) ?></h3>
                        <p class="mb-0">إجمالي المستخدمين</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-success"><?= count(array_filter($data['users'], function($user) { return $user['role'] === 'doctor'; })) ?></h3>
                        <p class="mb-0">الأطباء</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-warning"><?= count(array_filter($data['users'], function($user) { return $user['role'] === 'secretary'; })) ?></h3>
                        <p class="mb-0">السكرتارية</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-info"><?= count(array_filter($data['users'], function($user) { return $user['role'] === 'admin'; })) ?></h3>
                        <p class="mb-0">المدراء</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة المستخدمين
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($data['users'])): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الاسم الكامل</th>
                                    <th>اسم المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>العيادة</th>
                                    <th>الحالة</th>
                                    <th>آخر تسجيل دخول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($data['users'] as $user): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($user['full_name']) ?></strong>
                                        </td>
                                        <td><?= htmlspecialchars($user['username']) ?></td>
                                        <td><?= htmlspecialchars($user['email']) ?></td>
                                        <td>
                                            <?php
                                            $roleClass = '';
                                            $roleText = '';
                                            switch ($user['role']) {
                                                case 'admin':
                                                    $roleClass = 'badge bg-danger';
                                                    $roleText = 'مدير';
                                                    break;
                                                case 'doctor':
                                                    $roleClass = 'badge bg-success';
                                                    $roleText = 'طبيب';
                                                    break;
                                                case 'secretary':
                                                    $roleClass = 'badge bg-warning';
                                                    $roleText = 'سكرتير';
                                                    break;
                                                default:
                                                    $roleClass = 'badge bg-secondary';
                                                    $roleText = 'غير محدد';
                                            }
                                            ?>
                                            <span class="<?= $roleClass ?>">
                                                <?= $roleText ?>
                                            </span>
                                        </td>
                                        <td><?= htmlspecialchars($user['clinic_name'] ?? 'غير محدد') ?></td>
                                        <td>
                                            <?php if ($user['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($user['last_login']): ?>
                                                <?= date('d/m/Y H:i', strtotime($user['last_login'])) ?>
                                            <?php else: ?>
                                                <span class="text-muted">لم يسجل دخول</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewUser(<?= $user['id'] ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-success" onclick="editUser(<?= $user['id'] ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" onclick="toggleUserStatus(<?= $user['id'] ?>)">
                                                    <i class="fas fa-toggle-on"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(<?= $user['id'] ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد مستخدمين مسجلين</h5>
                        <p class="text-muted">ابدأ بإضافة مستخدم جديد</p>
                        <button class="btn btn-primary" onclick="addNewUser()">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مستخدم جديد
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // تفعيل القائمة الجانبية
        function activateNavLink() {
            const currentPage = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                const href = link.getAttribute('href');
                
                // استخراج اسم الملف من الرابط
                const fileName = href.split('/').pop();
                const currentFileName = currentPage.split('/').pop();
                
                // التحقق من تطابق اسم الملف
                if (fileName && fileName === currentFileName) {
                    link.classList.add('active');
                }
            });
        }

        // تبديل القائمة الجانبية (للموبايل)
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // دوال إدارة المستخدمين
        function addNewUser() {
            showAddUserModal();
        }

        function viewUser(id) {
            showUserDetailsModal(id);
        }

        function editUser(id) {
            showEditUserModal(id);
        }

        function toggleUserStatus(id) {
            const users = <?= json_encode($data['users']) ?>;
            const user = users.find(u => u.id == id);

            if (!user) {
                Swal.fire('خطأ', 'لم يتم العثور على المستخدم', 'error');
                return;
            }

            const action = user.is_active ? 'إلغاء تفعيل' : 'تفعيل';
            const icon = user.is_active ? 'warning' : 'question';

            Swal.fire({
                title: `${action} المستخدم`,
                text: `هل أنت متأكد من ${action} المستخدم: ${user.full_name}؟`,
                icon: icon,
                showCancelButton: true,
                confirmButtonColor: user.is_active ? '#d33' : '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: `نعم، ${action}`,
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `toggle-user.php?id=${id}`;

                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }

        function deleteUser(id) {
            const users = <?= json_encode($data['users']) ?>;
            const user = users.find(u => u.id == id);

            if (!user) {
                Swal.fire('خطأ', 'لم يتم العثور على المستخدم', 'error');
                return;
            }

            Swal.fire({
                title: 'تأكيد الحذف',
                text: `هل أنت متأكد من حذف المستخدم: ${user.full_name}؟ لا يمكن التراجع عن هذا الإجراء.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `delete-user.php?id=${id}`;

                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }

        // عرض modal تفاصيل المستخدم
        function showUserDetailsModal(userId) {
            const users = <?= json_encode($data['users']) ?>;
            const user = users.find(u => u.id == userId);

            if (!user) {
                Swal.fire('خطأ', 'لم يتم العثور على المستخدم', 'error');
                return;
            }

            const statusText = user.is_active ? 'نشط' : 'غير نشط';
            const statusClass = user.is_active ? 'success' : 'danger';
            const roleText = getRoleText(user.role);

            Swal.fire({
                title: `تفاصيل المستخدم: ${user.full_name}`,
                html: `
                    <div class="text-start">
                        <div class="row mb-3">
                            <div class="col-6"><strong>الاسم الكامل:</strong></div>
                            <div class="col-6">${user.full_name}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6"><strong>البريد الإلكتروني:</strong></div>
                            <div class="col-6">${user.email}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6"><strong>الهاتف:</strong></div>
                            <div class="col-6">${user.phone || 'غير محدد'}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6"><strong>الدور:</strong></div>
                            <div class="col-6">${roleText}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6"><strong>الحالة:</strong></div>
                            <div class="col-6"><span class="badge bg-${statusClass}">${statusText}</span></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6"><strong>تاريخ التسجيل:</strong></div>
                            <div class="col-6">${user.created_at || 'غير محدد'}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6"><strong>آخر تسجيل دخول:</strong></div>
                            <div class="col-6">${user.last_login || 'لم يسجل دخول'}</div>
                        </div>
                    </div>
                `,
                width: '600px',
                confirmButtonText: 'إغلاق'
            });
        }

        // عرض modal تعديل المستخدم
        function showEditUserModal(userId) {
            const users = <?= json_encode($data['users']) ?>;
            const user = users.find(u => u.id == userId);

            if (!user) {
                Swal.fire('خطأ', 'لم يتم العثور على المستخدم', 'error');
                return;
            }

            Swal.fire({
                title: 'تعديل المستخدم',
                html: `
                    <form id="editUserForm">
                        <div class="mb-3">
                            <label class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" name="full_name" value="${user.full_name}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email" value="${user.email}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الهاتف</label>
                            <input type="text" class="form-control" name="phone" value="${user.phone || ''}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الدور</label>
                            <select class="form-control" name="role" required>
                                <option value="doctor" ${user.role === 'doctor' ? 'selected' : ''}>طبيب</option>
                                <option value="secretary" ${user.role === 'secretary' ? 'selected' : ''}>سكرتير</option>
                                <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>مدير</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور الجديدة (اختياري)</label>
                            <input type="password" class="form-control" name="password" placeholder="اتركه فارغاً إذا لم ترد تغييرها">
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" name="is_active" ${user.is_active ? 'checked' : ''}>
                                <label class="form-check-label">المستخدم نشط</label>
                            </div>
                        </div>
                    </form>
                `,
                showCancelButton: true,
                confirmButtonText: 'حفظ التغييرات',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const form = document.getElementById('editUserForm');
                    const formData = new FormData(form);

                    return fetch(`edit-user.php?id=${userId}`, {
                        method: 'POST',
                        body: formData
                    }).then(response => {
                        if (response.ok) {
                            return true;
                        }
                        throw new Error('حدث خطأ أثناء التحديث');
                    });
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire('تم!', 'تم تحديث المستخدم بنجاح', 'success').then(() => {
                        location.reload();
                    });
                }
            });
        }

        // عرض modal إضافة مستخدم جديد
        function showAddUserModal() {
            Swal.fire({
                title: 'إضافة مستخدم جديد',
                html: `
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" name="full_name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الهاتف</label>
                            <input type="text" class="form-control" name="phone" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الدور</label>
                            <select class="form-control" name="role" required>
                                <option value="">اختر الدور</option>
                                <option value="doctor">طبيب</option>
                                <option value="secretary">سكرتير</option>
                                <option value="admin">مدير</option>
                            </select>
                        </div>
                    </form>
                `,
                showCancelButton: true,
                confirmButtonText: 'إضافة المستخدم',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const form = document.getElementById('addUserForm');
                    const formData = new FormData(form);

                    return fetch('add-user.php', {
                        method: 'POST',
                        body: formData
                    }).then(response => {
                        if (response.ok) {
                            return true;
                        }
                        throw new Error('حدث خطأ أثناء الإضافة');
                    });
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire('تم!', 'تم إضافة المستخدم بنجاح', 'success').then(() => {
                        location.reload();
                    });
                }
            });
        }

        // دالة مساعدة لترجمة الأدوار
        function getRoleText(role) {
            switch(role) {
                case 'doctor': return 'طبيب';
                case 'secretary': return 'سكرتير';
                case 'admin': return 'مدير';
                default: return 'غير محدد';
            }
        }

        // تفعيل القائمة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            activateNavLink();
        });
    </script>
</body>
</html> 