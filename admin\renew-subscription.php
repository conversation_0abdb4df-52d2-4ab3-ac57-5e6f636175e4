<?php
/**
 * تجديد اشتراك عيادة
 * Renew Clinic Subscription
 */

// تشغيل الجلسات
session_start();

// تعريف المسارات
define('ROOT_PATH', __DIR__ . '/..');
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');

// تحميل الإعدادات
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/core/AuthGuard.php';
require_once APP_PATH . '/models/Clinic.php';
require_once APP_PATH . '/controllers/Admin/DashboardController.php';

// التحقق من صلاحيات الأدمن
adminOnly();

// الحصول على معرف العيادة من URL
$clinicId = $_GET['id'] ?? null;

if (!$clinicId) {
    $_SESSION['notification'] = [
        'type' => 'error',
        'message' => 'معرف العيادة غير صحيح'
    ];
    header('Location: clinics.php');
    exit;
}

try {
    $controller = new DashboardController();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // معالجة تجديد الاشتراك
        $controller->renewSubscription($clinicId);
    } else {
        // عرض صفحة التجديد
        $controller->renewSubscription($clinicId);
    }
} catch (Exception $e) {
    $_SESSION['notification'] = [
        'type' => 'error',
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ];
    header('Location: clinics.php');
    exit;
}
?>
