<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $data['title'] ?? 'الرسائل - لوحة تحكم الأدمن' ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../public/favicon.svg">
    <link rel="icon" type="image/png" href="../public/favicon-32x32.png" sizes="32x32">
    <link rel="apple-touch-icon" href="../public/favicon.svg">
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #007BFF;
            --primary-sky-blue: #87CEEB;
            --primary-white: #FFFFFF;
            --primary-black: #333333;
            --light-gray: #F8F9FA;
            --border-color: #E0E0E0;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-hover: 0 4px 20px rgba(0,0,0,0.15);
            --admin-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--light-gray);
            color: var(--primary-black);
        }

        .sidebar {
            min-height: 100vh;
            background: var(--admin-gradient);
            color: var(--primary-white);
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            overflow-y: auto;
            max-height: 100vh;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 15px 20px;
            border-radius: 10px;
            margin: 5px 15px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid transparent;
            text-decoration: none;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.15);
            color: var(--primary-white);
            transform: translateX(-3px);
            border-color: rgba(255,255,255,0.2);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-white);
            color: var(--primary-blue);
            font-weight: 600;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .main-content {
            margin-right: 250px;
            padding: 25px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-white);
            box-shadow: var(--shadow);
            margin-bottom: 25px;
            border-radius: 10px;
            padding: 15px 20px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: var(--shadow);
            margin-bottom: 25px;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-2px);
        }

        .card-header {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border-radius: 0 !important;
            border: none;
            padding: 20px;
            font-weight: 600;
        }

        .btn-primary {
            background: var(--admin-gradient);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .table th {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border: none;
            font-weight: 600;
            padding: 15px;
        }

        .table td {
            padding: 15px;
            border-color: var(--border-color);
            vertical-align: middle;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
        }

        .badge {
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.8rem;
        }

        .admin-header {
            background: var(--admin-gradient);
            color: var(--primary-white);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .message-card {
            background: var(--primary-white);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border-right: 4px solid var(--primary-blue);
        }

        .message-card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-3px);
        }

        .message-card.unread {
            border-right-color: #dc3545;
            background-color: #fff5f5;
        }

        .message-meta {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .message-preview {
            color: #495057;
            margin-top: 10px;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <h4 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    لوحة تحكم الأدمن
                </h4>
                <small class="text-white-50">نظام إدارة العيادات</small>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="dashboard.php" data-page="dashboard">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    الرئيسية
                </a>
                <a class="nav-link" href="clinics.php" data-page="clinics">
                    <i class="fas fa-hospital me-2"></i>
                    العيادات
                </a>
                <a class="nav-link" href="subscriptions.php" data-page="subscriptions">
                    <i class="fas fa-credit-card me-2"></i>
                    الاشتراكات
                </a>
                <a class="nav-link" href="users.php" data-page="users">
                    <i class="fas fa-users me-2"></i>
                    المستخدمين
                </a>
                <a class="nav-link" href="revenue.php" data-page="revenue">
                    <i class="fas fa-chart-line me-2"></i>
                    الإيرادات
                </a>
                <a class="nav-link" href="messages.php" data-page="messages">
                    <i class="fas fa-envelope me-2"></i>
                    الرسائل
                </a>
                <a class="nav-link" href="settings.php" data-page="settings">
                    <i class="fas fa-cog me-2"></i>
                    الإعدادات
                </a>
                <hr class="text-white-50">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    تسجيل الخروج
                </a>
            </nav>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="admin-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="fas fa-envelope me-3"></i>
                        الرسائل
                    </h1>
                    <p class="mb-0 mt-2">عرض وإدارة جميع الرسائل الواردة من المستخدمين</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-light" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                        <h4><?= $data['stats']['total'] ?? 0 ?></h4>
                        <p class="text-muted">إجمالي الرسائل</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-envelope-open fa-2x text-warning mb-2"></i>
                        <h4><?= $data['stats']['new'] ?? 0 ?></h4>
                        <p class="text-muted">رسائل جديدة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-eye fa-2x text-info mb-2"></i>
                        <h4><?= $data['stats']['read'] ?? 0 ?></h4>
                        <p class="text-muted">رسائل مقروءة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-reply fa-2x text-success mb-2"></i>
                        <h4><?= $data['stats']['replied'] ?? 0 ?></h4>
                        <p class="text-muted">رسائل مجاب عليها</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages Content -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    رسائل التواصل
                </h5>
                <div class="d-flex">
                    <input type="text" class="form-control me-2" id="searchMessages" placeholder="البحث في الرسائل...">
                    <button class="btn btn-outline-light" onclick="searchMessages()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Filter Buttons -->
                <div class="mb-4">
                    <button class="btn btn-primary me-2" onclick="filterMessages('all')">
                        <i class="fas fa-list me-1"></i>
                        جميع الرسائل
                    </button>
                    <button class="btn btn-warning me-2" onclick="filterMessages('new')">
                        <i class="fas fa-envelope me-1"></i>
                        رسائل جديدة
                    </button>
                    <button class="btn btn-info me-2" onclick="filterMessages('read')">
                        <i class="fas fa-eye me-1"></i>
                        رسائل مقروءة
                    </button>
                    <button class="btn btn-success me-2" onclick="filterMessages('replied')">
                        <i class="fas fa-reply me-1"></i>
                        رسائل مجاب عليها
                    </button>
                </div>

                <!-- Messages List -->
                <div id="messagesList">
                    <?php if (empty($data['messages'])): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد رسائل</h5>
                            <p class="text-muted">لم يتم إرسال أي رسائل من نموذج التواصل بعد</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($data['messages'] as $message): ?>
                            <div class="message-card <?= $message['status'] ?>"
                                 data-status="<?= $message['status'] ?>"
                                 data-message-id="<?= $message['id'] ?>"
                                 data-name="<?= htmlspecialchars($message['name']) ?>"
                                 data-email="<?= htmlspecialchars($message['email']) ?>"
                                 data-phone="<?= htmlspecialchars($message['phone'] ?? '') ?>"
                                 data-subject="<?= htmlspecialchars($message['subject']) ?>"
                                 data-message="<?= htmlspecialchars($message['message']) ?>"
                                 data-created="<?= $message['created_at'] ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="mb-0"><?= htmlspecialchars($message['name']) ?></h6>
                                            <span class="badge bg-<?= $message['status'] === 'new' ? 'danger' : ($message['status'] === 'read' ? 'info' : 'success') ?>">
                                                <?php
                                                switch($message['status']) {
                                                    case 'new': echo 'جديدة'; break;
                                                    case 'read': echo 'مقروءة'; break;
                                                    case 'replied': echo 'مجاب عليها'; break;
                                                    default: echo 'غير معروفة';
                                                }
                                                ?>
                                            </span>
                                        </div>
                                        <div class="message-meta mb-2">
                                            <span class="me-3">
                                                <i class="fas fa-envelope me-1"></i>
                                                <?= htmlspecialchars($message['email']) ?>
                                            </span>
                                            <span class="me-3">
                                                <i class="fas fa-calendar me-1"></i>
                                                <?= date('Y-m-d H:i', strtotime($message['created_at'])) ?>
                                            </span>
                                        </div>
                                        <h6 class="mb-2"><?= htmlspecialchars($message['subject']) ?></h6>
                                        <div class="message-preview">
                                            <?= nl2br(htmlspecialchars(substr($message['message'], 0, 200))) ?><?= strlen($message['message']) > 200 ? '...' : '' ?>
                                        </div>
                                        <?php if (!empty($message['admin_notes'])): ?>
                                            <div class="alert alert-info mt-2">
                                                <strong>ملاحظات الأدمن:</strong> <?= htmlspecialchars($message['admin_notes']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="btn-group ms-3">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewMessage(<?= $message['id'] ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="replyMessage(<?= $message['id'] ?>)">
                                            <i class="fas fa-reply"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteMessage(<?= $message['id'] ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Message View Modal -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">عرض الرسالة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                    <!-- Message content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-success" onclick="markAsRead()">تحديد كمقروءة</button>
                    <button type="button" class="btn btn-primary" onclick="replyToMessage()">الرد على الرسالة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentMessageId = null;

        // تفعيل القائمة الجانبية
        function activateNavLink() {
            const currentPage = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                const href = link.getAttribute('href');
                
                // استخراج اسم الملف من الرابط
                const fileName = href.split('/').pop();
                const currentFileName = currentPage.split('/').pop();
                
                // التحقق من تطابق اسم الملف
                if (fileName && fileName === currentFileName) {
                    link.classList.add('active');
                }
            });
        }

        // تبديل القائمة الجانبية (للموبايل)
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        function filterMessages(status) {
            const messages = document.querySelectorAll('.message-card');
            messages.forEach(message => {
                if (status === 'all' || message.dataset.status === status) {
                    message.style.display = 'block';
                } else {
                    message.style.display = 'none';
                }
            });
        }

        function searchMessages() {
            const searchTerm = document.getElementById('searchMessages').value.toLowerCase();
            const messages = document.querySelectorAll('.message-card');
            
            messages.forEach(message => {
                const text = message.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    message.style.display = 'block';
                } else {
                    message.style.display = 'none';
                }
            });
        }

        function viewMessage(messageId) {
            currentMessageId = messageId;

            // جلب البيانات من data attributes
            const messageCard = document.querySelector(`[data-message-id="${messageId}"]`);

            if (!messageCard) {
                // البحث في البيانات المرسلة من الخادم كبديل
                const allMessages = <?= json_encode($data['messages']) ?>;
                const messageData = allMessages.find(msg => msg.id == messageId);

                if (messageData) {
                    displayMessageInModal(messageData);
                } else {
                    alert('لم يتم العثور على الرسالة');
                }
                return;
            }

            // استخراج البيانات من data attributes
            const messageData = {
                id: messageId,
                name: messageCard.dataset.name,
                email: messageCard.dataset.email,
                phone: messageCard.dataset.phone,
                subject: messageCard.dataset.subject,
                message: messageCard.dataset.message,
                created_at: messageCard.dataset.created,
                status: messageCard.dataset.status
            };

            displayMessageInModal(messageData);
        }

        function displayMessageInModal(messageData) {
            
            document.getElementById('messageModalBody').innerHTML = `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong><i class="fas fa-user text-primary me-2"></i>الاسم:</strong>
                        <div class="mt-1 p-2 bg-light rounded">${messageData.name || 'غير محدد'}</div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong><i class="fas fa-envelope text-primary me-2"></i>البريد الإلكتروني:</strong>
                        <div class="mt-1 p-2 bg-light rounded">
                            <a href="mailto:${messageData.email}" class="text-decoration-none">${messageData.email || 'غير محدد'}</a>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong><i class="fas fa-phone text-primary me-2"></i>رقم الهاتف:</strong>
                        <div class="mt-1 p-2 bg-light rounded">
                            ${messageData.phone ? `<a href="tel:${messageData.phone}" class="text-decoration-none">${messageData.phone}</a>` : 'غير محدد'}
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong><i class="fas fa-tag text-primary me-2"></i>الموضوع:</strong>
                        <div class="mt-1 p-2 bg-light rounded">${messageData.subject || 'غير محدد'}</div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong><i class="fas fa-clock text-primary me-2"></i>تاريخ الإرسال:</strong>
                        <div class="mt-1 p-2 bg-light rounded">${messageData.created_at || 'غير محدد'}</div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong><i class="fas fa-info-circle text-primary me-2"></i>الحالة:</strong>
                        <div class="mt-1 p-2 bg-light rounded">
                            <span class="badge bg-${messageData.status === 'new' ? 'warning' : messageData.status === 'read' ? 'info' : 'success'}">
                                ${messageData.status === 'new' ? 'جديدة' : messageData.status === 'read' ? 'مقروءة' : 'مجاب عليها'}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 mb-3">
                        <strong><i class="fas fa-comment text-primary me-2"></i>الرسالة:</strong>
                        <div class="mt-2 p-3 border rounded bg-white" style="min-height: 100px; white-space: pre-wrap;">
                            ${messageData.message || 'غير محدد'}
                        </div>
                    </div>
                </div>
            `;
            
            new bootstrap.Modal(document.getElementById('messageModal')).show();
        }

        function replyMessage(messageId) {
            // في التطبيق الحقيقي، سيتم فتح نافذة الرد
            alert('سيتم إضافة وظيفة الرد على الرسائل قريباً');
        }

        function deleteMessage(messageId) {
            if (confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
                // في التطبيق الحقيقي، سيتم إرسال طلب حذف للخادم
                alert('تم حذف الرسالة بنجاح');
                location.reload();
            }
        }

        function markAsRead() {
            if (currentMessageId) {
                // في التطبيق الحقيقي، سيتم تحديث حالة الرسالة
                alert('تم تحديد الرسالة كمقروءة');
                location.reload();
            }
        }

        function replyToMessage() {
            // في التطبيق الحقيقي، سيتم فتح نافذة الرد
            alert('سيتم إضافة وظيفة الرد على الرسائل قريباً');
        }

        // تحديث البحث عند الكتابة
        document.getElementById('searchMessages').addEventListener('input', searchMessages);

        // تفعيل القائمة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            activateNavLink();
        });
    </script>
</body>
</html> 