<?php
/**
 * كنترولر لوحة تحكم الأدمن
 * Admin Dashboard Controller
 */

class DashboardController extends Controller
{
    public function __construct()
    {
        parent::__construct();
        adminOnly();
    }

    public function index()
    {
        // جلب إحصائيات لوحة تحكم الأدمن
        $stats = $this->getAdminStats();
        
        $data = [
            'title' => 'لوحة تحكم الأدمن - نظام حكيم',
            'stats' => $stats
        ];
        
        $this->view('admin/dashboard', $data);
    }

    private function getAdminStats()
    {
        $clinicModel = $this->model('Clinic');
        $userModel = $this->model('User');
        
        return [
            'total_clinics' => $clinicModel->getTotalClinics(),
            'active_subscriptions' => $clinicModel->getActiveSubscriptions(),
            'expired_subscriptions' => $clinicModel->getExpiredSubscriptions(),
            'total_users' => $userModel->getTotalUsers(),
            'total_revenue' => $clinicModel->getTotalRevenue(),
            'recent_contacts' => $this->getRecentContacts(),
            'subscription_stats' => $clinicModel->getSubscriptionStats()
        ];
    }

    private function getRecentContacts()
    {
        $contactModel = $this->model('ContactMessage');
        $limit = 5;
        return $contactModel->getMessagesOrdered($limit); // آخر 5 رسائل
    }

    /**
     * صفحة إدارة العيادات
     * Clinics management page
     */
    public function clinics()
    {
        $clinicModel = $this->model('Clinic');
        $clinics = $clinicModel->getClinicsWithSubscriptions();
        
        $data = [
            'title' => 'إدارة العيادات - لوحة تحكم الأدمن',
            'clinics' => $clinics
        ];
        
        $this->view('admin/clinics', $data);
    }

    /**
     * صفحة إدارة الاشتراكات
     * Subscriptions management page
     */
    public function subscriptions()
    {
        $clinicModel = $this->model('Clinic');
        $expiredClinics = $clinicModel->getExpiredClinics();
        $expiringSoon = $clinicModel->getExpiringSoon(7);
        
        $data = [
            'title' => 'إدارة الاشتراكات - لوحة تحكم الأدمن',
            'expired_clinics' => $expiredClinics,
            'expiring_soon' => $expiringSoon
        ];
        
        $this->view('admin/subscriptions', $data);
    }

    /**
     * عرض تفاصيل عيادة
     * View clinic details
     */
    public function viewClinic($clinicId) {
        adminOnly();

        $clinicModel = $this->model('Clinic');
        $clinic = $clinicModel->find($clinicId);

        if (!$clinic) {
            $this->notify('العيادة غير موجودة', 'error');
            $this->redirect('admin/clinics');
        }

        // جلب إحصائيات العيادة
        $stats = $clinicModel->getClinicStats($clinicId);

        $data = [
            'title' => 'تفاصيل العيادة - ' . $clinic['name'],
            'clinic' => $clinic,
            'stats' => $stats
        ];

        $this->view('admin/clinic-details', $data);
    }

    /**
     * تعديل عيادة
     * Edit clinic
     */
    public function editClinic($clinicId) {
        adminOnly();

        $clinicModel = $this->model('Clinic');
        $clinic = $clinicModel->find($clinicId);

        if (!$clinic) {
            $this->notify('العيادة غير موجودة', 'error');
            $this->redirect('admin/clinics');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleEditClinic($clinicId);
        }

        $data = [
            'title' => 'تعديل العيادة - ' . $clinic['name'],
            'clinic' => $clinic
        ];

        $this->view('admin/edit-clinic', $data);
    }

    /**
     * حذف عيادة
     * Delete clinic
     */
    public function deleteClinic($clinicId) {
        adminOnly();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->notify('طريقة غير صحيحة', 'error');
            $this->redirect('admin/clinics');
        }

        $clinicModel = $this->model('Clinic');
        $clinic = $clinicModel->find($clinicId);

        if (!$clinic) {
            $this->notify('العيادة غير موجودة', 'error');
            $this->redirect('admin/clinics');
        }

        // حذف العيادة
        if ($clinicModel->delete($clinicId)) {
            $this->notify('تم حذف العيادة بنجاح', 'success');
        } else {
            $this->notify('حدث خطأ أثناء حذف العيادة', 'error');
        }

        $this->redirect('admin/clinics');
    }

    /**
     * تجديد اشتراك عيادة
     * Renew clinic subscription
     */
    public function renewSubscription($clinicId) {
        adminOnly();

        $clinicModel = $this->model('Clinic');
        $clinic = $clinicModel->find($clinicId);

        if (!$clinic) {
            $this->notify('العيادة غير موجودة', 'error');
            $this->redirect('admin/clinics');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleRenewSubscription($clinicId);
        }

        $data = [
            'title' => 'تجديد اشتراك - ' . $clinic['name'],
            'clinic' => $clinic
        ];

        $this->view('admin/renew-subscription', $data);
    }

    /**
     * إضافة عيادة جديدة
     * Add new clinic
     */
    public function addClinic() {
        adminOnly();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleAddClinic();
        }

        $data = [
            'title' => 'إضافة عيادة جديدة'
        ];

        $this->view('admin/add-clinic', $data);
    }

    /**
     * صفحة إدارة المستخدمين
     * Users management page
     */
    public function users()
    {
        $userModel = $this->model('User');
        $users = $userModel->getAllUsers();
        
        $data = [
            'title' => 'إدارة المستخدمين - لوحة تحكم الأدمن',
            'users' => $users
        ];
        
        $this->view('admin/users', $data);
    }

    /**
     * صفحة الإيرادات
     * Revenue page
     */
    public function revenue()
    {
        $clinicModel = $this->model('Clinic');
        $totalRevenue = $clinicModel->getTotalRevenue();
        $monthlyRevenue = $clinicModel->getMonthlyRevenue();
        
        $data = [
            'title' => 'الإيرادات - لوحة تحكم الأدمن',
            'total_revenue' => $totalRevenue,
            'monthly_revenue' => $monthlyRevenue
        ];
        
        $this->view('admin/revenue', $data);
    }

    /**
     * صفحة الرسائل
     * Messages page
     */
    public function messages()
    {
        $contactModel = $this->model('ContactMessage');
        $limit = 50;
        $messages = $contactModel->getMessagesOrdered($limit);
        $stats = $contactModel->getMessageStats();
        
        $data = [
            'title' => 'الرسائل - لوحة تحكم الأدمن',
            'messages' => $messages,
            'stats' => $stats
        ];
        
        $this->view('admin/messages', $data);
    }

    /**
     * صفحة الإعدادات
     * Settings page
     */
    public function settings()
    {
        $data = [
            'title' => 'إعدادات النظام - لوحة تحكم الأدمن'
        ];
        
        $this->view('admin/settings', $data);
    }

    /**
     * معالجة تعديل العيادة
     * Handle edit clinic
     */
    private function handleEditClinic($clinicId) {
        $data = $this->sanitize($_POST);

        $errors = $this->validate($data, [
            'name' => 'required|min:2',
            'email' => 'required|email',
            'phone' => 'required',
            'subscription_plan' => 'required',
            'subscription_end' => 'required'
        ]);

        if (empty($errors)) {
            $clinicModel = $this->model('Clinic');
            $updateData = [
                'name' => $data['name'],
                'email' => $data['email'],
                'phone' => $data['phone'],
                'subscription_plan' => $data['subscription_plan'],
                'subscription_end' => $data['subscription_end'],
                'is_active' => isset($data['is_active']) ? 1 : 0
            ];

            if ($clinicModel->update($clinicId, $updateData)) {
                $this->notify('تم تحديث العيادة بنجاح', 'success');
                $this->redirect('admin/clinics');
            } else {
                $this->notify('حدث خطأ أثناء تحديث العيادة', 'error');
            }
        } else {
            $this->notify('يرجى تصحيح الأخطاء: ' . implode(', ', $errors), 'error');
        }
    }

    /**
     * معالجة تجديد الاشتراك
     * Handle renew subscription
     */
    private function handleRenewSubscription($clinicId) {
        $data = $this->sanitize($_POST);

        $errors = $this->validate($data, [
            'subscription_plan' => 'required',
            'subscription_months' => 'required|numeric|min:1'
        ]);

        if (empty($errors)) {
            $clinicModel = $this->model('Clinic');
            $clinic = $clinicModel->find($clinicId);

            // حساب تاريخ انتهاء الاشتراك الجديد
            $currentEnd = $clinic['subscription_end'] ?? date('Y-m-d');
            $newEnd = date('Y-m-d', strtotime($currentEnd . ' + ' . $data['subscription_months'] . ' months'));

            $updateData = [
                'subscription_plan' => $data['subscription_plan'],
                'subscription_end' => $newEnd,
                'is_active' => 1
            ];

            if ($clinicModel->update($clinicId, $updateData)) {
                $this->notify('تم تجديد الاشتراك بنجاح', 'success');
                $this->redirect('admin/clinics');
            } else {
                $this->notify('حدث خطأ أثناء تجديد الاشتراك', 'error');
            }
        } else {
            $this->notify('يرجى تصحيح الأخطاء: ' . implode(', ', $errors), 'error');
        }
    }

    /**
     * معالجة إضافة عيادة جديدة
     * Handle add new clinic
     */
    private function handleAddClinic() {
        $data = $this->sanitize($_POST);

        $errors = $this->validate($data, [
            'name' => 'required|min:2',
            'email' => 'required|email',
            'phone' => 'required',
            'subscription_plan' => 'required',
            'subscription_months' => 'required|numeric|min:1'
        ]);

        if (empty($errors)) {
            $clinicModel = $this->model('Clinic');

            // حساب تاريخ انتهاء الاشتراك
            $subscriptionEnd = date('Y-m-d', strtotime('+' . $data['subscription_months'] . ' months'));

            $clinicData = [
                'name' => $data['name'],
                'email' => $data['email'],
                'phone' => $data['phone'],
                'subscription_plan' => $data['subscription_plan'],
                'subscription_start' => date('Y-m-d'),
                'subscription_end' => $subscriptionEnd,
                'is_active' => 1
            ];

            if ($clinicModel->create($clinicData)) {
                $this->notify('تم إضافة العيادة بنجاح', 'success');
                $this->redirect('admin/clinics');
            } else {
                $this->notify('حدث خطأ أثناء إضافة العيادة', 'error');
            }
        } else {
            $this->notify('يرجى تصحيح الأخطاء: ' . implode(', ', $errors), 'error');
        }
    }
}