<?php
/**
 * كنترولر لوحة تحكم الأدمن
 * Admin Dashboard Controller
 */

class DashboardController extends Controller
{
    public function __construct()
    {
        parent::__construct();
        adminOnly();
    }

    public function index()
    {
        // جلب إحصائيات لوحة تحكم الأدمن
        $stats = $this->getAdminStats();
        
        $data = [
            'title' => 'لوحة تحكم الأدمن - نظام حكيم',
            'stats' => $stats
        ];
        
        $this->view('admin/dashboard', $data);
    }

    private function getAdminStats()
    {
        $clinicModel = $this->model('Clinic');
        $userModel = $this->model('User');
        
        return [
            'total_clinics' => $clinicModel->getTotalClinics(),
            'active_subscriptions' => $clinicModel->getActiveSubscriptions(),
            'expired_subscriptions' => $clinicModel->getExpiredSubscriptions(),
            'total_users' => $userModel->getTotalUsers(),
            'total_revenue' => $clinicModel->getTotalRevenue(),
            'recent_contacts' => $this->getRecentContacts(),
            'subscription_stats' => $clinicModel->getSubscriptionStats()
        ];
    }

    private function getRecentContacts()
    {
        $contactModel = $this->model('ContactMessage');
        $limit = 5;
        return $contactModel->getMessagesOrdered($limit); // آخر 5 رسائل
    }

    /**
     * صفحة إدارة العيادات
     * Clinics management page
     */
    public function clinics()
    {
        $clinicModel = $this->model('Clinic');
        $clinics = $clinicModel->getClinicsWithSubscriptions();
        
        $data = [
            'title' => 'إدارة العيادات - لوحة تحكم الأدمن',
            'clinics' => $clinics
        ];
        
        $this->view('admin/clinics', $data);
    }

    /**
     * صفحة إدارة الاشتراكات
     * Subscriptions management page
     */
    public function subscriptions()
    {
        $clinicModel = $this->model('Clinic');
        $expiredClinics = $clinicModel->getExpiredClinics();
        $expiringSoon = $clinicModel->getExpiringSoon(7);
        
        $data = [
            'title' => 'إدارة الاشتراكات - لوحة تحكم الأدمن',
            'expired_clinics' => $expiredClinics,
            'expiring_soon' => $expiringSoon
        ];
        
        $this->view('admin/subscriptions', $data);
    }

    /**
     * صفحة إدارة المستخدمين
     * Users management page
     */
    public function users()
    {
        $userModel = $this->model('User');
        $users = $userModel->getAllUsers();
        
        $data = [
            'title' => 'إدارة المستخدمين - لوحة تحكم الأدمن',
            'users' => $users
        ];
        
        $this->view('admin/users', $data);
    }

    /**
     * صفحة الإيرادات
     * Revenue page
     */
    public function revenue()
    {
        $clinicModel = $this->model('Clinic');
        $totalRevenue = $clinicModel->getTotalRevenue();
        $monthlyRevenue = $clinicModel->getMonthlyRevenue();
        
        $data = [
            'title' => 'الإيرادات - لوحة تحكم الأدمن',
            'total_revenue' => $totalRevenue,
            'monthly_revenue' => $monthlyRevenue
        ];
        
        $this->view('admin/revenue', $data);
    }

    /**
     * صفحة الرسائل
     * Messages page
     */
    public function messages()
    {
        $contactModel = $this->model('ContactMessage');
        $limit = 50;
        $messages = $contactModel->getMessagesOrdered($limit);
        $stats = $contactModel->getMessageStats();
        
        $data = [
            'title' => 'الرسائل - لوحة تحكم الأدمن',
            'messages' => $messages,
            'stats' => $stats
        ];
        
        $this->view('admin/messages', $data);
    }

    /**
     * صفحة الإعدادات
     * Settings page
     */
    public function settings()
    {
        $data = [
            'title' => 'إعدادات النظام - لوحة تحكم الأدمن'
        ];
        
        $this->view('admin/settings', $data);
    }
} 