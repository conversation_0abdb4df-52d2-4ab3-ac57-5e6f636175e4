<?php
/**
 * صفحة إدارة المواعيد
 * Appointments Management
 */

// تشغيل الجلسات
session_start();

// تعريف المسارات
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('VIEWS_PATH', APP_PATH . '/views');

// تحميل الإعدادات
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/models/Appointment.php';
require_once APP_PATH . '/models/Patient.php';
require_once APP_PATH . '/controllers/AppointmentsController.php';
require_once APP_PATH . '/core/AuthGuard.php';
requireAuth();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// تشغيل كونترولر المواعيد
$controller = new AppointmentsController();
$action = $_GET['action'] ?? 'index';
$id = $_GET['id'] ?? null;

switch ($action) {
    case 'index':
        $controller->index();
        break;
    case 'calendar':
        $controller->calendar();
        break;
    case 'add':
        $controller->add();
        break;
    case 'edit':
        if ($id) {
            $controller->edit($id);
        } else {
            header('Location: appointments.php');
        }
        break;
    case 'view':
        if ($id) {
            $controller->view($id);
        } else {
            header('Location: appointments.php');
        }
        break;
    case 'confirm':
        if ($id) {
            $controller->confirm($id);
        } else {
            header('Location: appointments.php');
        }
        break;
    case 'cancel':
        if ($id) {
            $controller->cancel($id);
        } else {
            header('Location: appointments.php');
        }
        break;
    case 'delete':
        if ($id) {
            $controller->delete($id);
        } else {
            header('Location: appointments.php');
        }
        break;
    default:
        $controller->index();
        break;
}
?>
