<?php
/**
 * إضافة مستخدم جديد
 * Add New User
 */

// تشغيل الجلسات
session_start();

// تعريف المسارات
define('ROOT_PATH', __DIR__ . '/..');
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');

// تحميل الإعدادات
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/core/AuthGuard.php';
require_once APP_PATH . '/models/User.php';
require_once APP_PATH . '/controllers/Admin/DashboardController.php';

// التحقق من صلاحيات الأدمن
adminOnly();

try {
    $controller = new DashboardController();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // معالجة إضافة المستخدم
        $controller->addUser();
    } else {
        // عرض صفحة الإضافة
        $controller->addUser();
    }
} catch (Exception $e) {
    $_SESSION['notification'] = [
        'type' => 'error',
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ];
    header('Location: users.php');
    exit;
}
?>
