<?php
/**
 * تغيير حالة المستخدم (تفعيل/إلغاء تفعيل)
 * Toggle User Status
 */

// تشغيل الجلسات
session_start();

// تعريف المسارات
define('ROOT_PATH', __DIR__ . '/..');
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');

// تحميل الإعدادات
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/core/AuthGuard.php';
require_once APP_PATH . '/models/User.php';
require_once APP_PATH . '/controllers/Admin/DashboardController.php';

// التحقق من صلاحيات الأدمن
adminOnly();

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: users.php');
    exit;
}

// الحصول على معرف المستخدم من URL
$userId = $_GET['id'] ?? null;

if (!$userId) {
    $_SESSION['notification'] = [
        'type' => 'error',
        'message' => 'معرف المستخدم غير صحيح'
    ];
    header('Location: users.php');
    exit;
}

try {
    $controller = new DashboardController();
    $controller->toggleUserStatus($userId);
} catch (Exception $e) {
    $_SESSION['notification'] = [
        'type' => 'error',
        'message' => 'حدث خطأ أثناء تغيير حالة المستخدم: ' . $e->getMessage()
    ];
    header('Location: users.php');
    exit;
}
?>
